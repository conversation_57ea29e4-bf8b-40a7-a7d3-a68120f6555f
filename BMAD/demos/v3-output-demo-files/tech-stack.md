# Technology Stack

The following table outlines the definitive technology selections for the BMad Daily Digest project:

| Category             | Technology                     | Version / Details                      | Description / Purpose                                                                 | Justification (Optional)                                                                 |
| :------------------- | :----------------------------- | :------------------------------------- | :------------------------------------------------------------------------------------ | :--------------------------------------------------------------------------------------- |
| **Languages** | TypeScript                     | Latest stable (e.g., 5.x)              | Primary language for backend and frontend.                                            | Consistency, strong typing.                                                              |
| **Runtime** | Node.js                        | 22.x                                   | Server-side environment for backend & Next.js.                                        | User preference, performance.                                                            |
| **Frameworks (Frontend)** | Next.js (with React)           | Latest stable (e.g., 14.x)             | Frontend web application framework.                                                   | User preference, SSG, DX.                                                                |
| **Frameworks (Backend)** | AWS Lambda (Node.js runtime)   | N/A                                    | Execution environment for serverless functions.                                       | Serverless architecture.                                                                 |
|                      | AWS Step Functions             | N/A                                    | Orchestration of backend workflows.                                                   | Robust state management, retries.                                                        |
| **Databases** | AWS DynamoDB                   | N/A                                    | NoSQL database for metadata.                                                          | Scalability, serverless, free-tier.                                                      |
| **Cloud Platform** | AWS                            | N/A                                    | Primary cloud provider.                                                               | Comprehensive services, serverless.                                                      |
| **Cloud Services** | AWS Lambda, API Gateway, S3, CloudFront, EventBridge Scheduler, CloudWatch, IAM, ACM | N/A                                    | Core services for application hosting and operation.                                  | Standard AWS serverless stack.                                                           |
| **Infrastructure as Code (IaC)** | AWS CDK (TypeScript)           | v2.x Latest stable                   | Defining cloud infrastructure.                                                        | User preference, TypeScript, repeatability.                                              |
| **UI Libraries (Frontend)** | Tailwind CSS                 | Latest stable (e.g., 3.x)              | Utility-first CSS framework.                                                          | User preference, customization.                                                          |
|                      | shadcn/ui                      | Latest stable                          | Accessible UI components.                                                             | User preference, base for themed components.                                             |
| **HTTP Client (Backend)** | axios                          | Latest stable                          | Making HTTP requests from backend.                                                    | User preference, feature-rich.                                                           |
| **SDKs / Core Libraries (Backend)** | AWS SDK for JavaScript/TypeScript | v3.x (Latest stable)                 | Programmatic interaction with AWS services.                                           | Official AWS SDK, modular.                                                               |
| **Scraping / Content Extraction** | Cheerio                        | Latest stable                          | Server-side HTML parsing.                                                             | Efficient for static HTML.                                                               |
|                      | @mozilla/readability (JS port) | Latest stable                          | Extracting primary readable article content.                                          | Key for isolating main content.                                                          |
|                      | Playwright (or Puppeteer)      | Latest stable                          | Browser automation (if required for dynamic content).                                 | Handles dynamic sites; use judiciously.                                                  |
| **Bundling (Backend)**| esbuild                        | Latest stable                          | Bundling TypeScript Lambda functions.                                                 | User preference, speed.                                                                  |
| **Logging (Backend)** | Pino                           | Latest stable                          | Structured, low-overhead logging.                                                     | Better observability, JSON logs for CloudWatch.                                          |
| **Testing (Backend)**| Jest, ESLint, Prettier         | Latest stable                          | Unit/integration testing, linting, formatting.                                        | Code quality, consistency.                                                               |
| **Testing (Frontend)**| Jest, React Testing Library, ESLint, Prettier | Latest stable                          | Unit/component testing, linting, formatting.                                          | Code quality, consistency.                                                               |
| **CI/CD** | GitHub Actions                 | N/A                                    | Automation of build, test, quality checks.                                            | Integration with GitHub.                                                                 |
| **External APIs** | Algolia HN Search API, Play.ai PlayNote API | v1 (for both)                        | Data sources and audio generation.                                                    | Core to product functionality.                                                           |

*Note: "Latest stable" versions should be pinned to specific versions in `package.json` files during development.* 