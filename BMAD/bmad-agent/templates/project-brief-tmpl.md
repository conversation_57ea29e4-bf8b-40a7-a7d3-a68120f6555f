# Project Brief: {Project Name}

## Introduction / Problem Statement

{Describe the core idea, the problem being solved, or the opportunity being addressed. Why is this project needed?}

## Vision & Goals

- **Vision:** {Describe the high-level desired future state or impact of this project.}
- **Primary Goals:** {List 2-5 specific, measurable, achievable, relevant, time-bound (SMART) goals for the Minimum Viable Product (MVP).}
  - Goal 1: ...
  - Goal 2: ...
- **Success Metrics (Initial Ideas):** {How will we measure if the project/MVP is successful? List potential KPIs.}

## Target Audience / Users

{Describe the primary users of this product/system. Who are they? What are their key characteristics or needs relevant to this project?}

## Key Features / Scope (High-Level Ideas for MVP)

{List the core functionalities or features envisioned for the MVP. Keep this high-level; details will go in the PRD/Epics.}

- Feature Idea 1: ...
- Feature Idea 2: ...
- Feature Idea N: ...

## Post MVP Features / Scope and Ideas

{List the core functionalities or features envisioned as potential for POST MVP. Keep this high-level; details will go in the PRD/Epics/Architecture.}

- Feature Idea 1: ...
- Feature Idea 2: ...
- Feature Idea N: ...

## Known Technical Constraints or Preferences

- **Constraints:** {List any known limitations and technical mandates or preferences - e.g., budget, timeline, specific technology mandates, required integrations, compliance needs.}
- **Initial Architectural Preferences (if any):** {Capture any early thoughts or strong preferences regarding repository structure (e.g., monorepo, polyrepo) and overall service architecture (e.g., monolith, microservices, serverless components). This is not a final decision point but for initial awareness.}
- **Risks:** {Identify potential risks - e.g., technical challenges, resource availability, market acceptance, dependencies.}
- **User Preferences:** {Any specific requests from the user that are not a high level feature that could direct technology or library choices, or anything else that came up in the brainstorming or drafting of the PRD that is not included in prior document sections}

## Relevant Research (Optional)

{Link to or summarize findings from any initial research conducted (e.g., `deep-research-report-BA.md`).}

## PM Prompt

This Project Brief provides the full context for {Project Name}. Please start in 'PRD Generation Mode', review the brief thoroughly to work with the user to create the PRD section by section 1 at a time, asking for any necessary clarification or suggesting improvements as your mode 1 programming allows.

<example_handoff_prompt>
This Project Brief provides the full context for Mealmate. Please start in 'PRD Generation Mode', review the brief thoroughly to work with the user to create the PRD section by section 1 at a time, asking for any necessary clarification or suggesting improvements as your mode 1 programming allows.</example_handoff_prompt>
