<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聆花文化ERP系统 - 高保真原型设计</title>
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap">
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/mermaid/8.14.0/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-self;
            color: #333;
            line-height: 1.6;
        }
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Noto Serif SC', serif;
            font-weight: 600;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .prototype-container {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: #f8fafc;
        }
        .prototype-screen {
            margin-bottom: 2rem;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .prototype-screen img {
            max-width: 100%;
            height: auto;
            border-radius: 0.25rem;
        }
        .mermaid {
            margin: 2rem 0;
        }
        .pain-point {
            background-color: #FEF3C7;
            border-left: 4px solid #F59E0B;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.25rem;
        }
        .solution {
            background-color: #DCFCE7;
            border-left: 4px solid #10B981;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.25rem;
        }
        .design-feature {
            background-color: #EFF6FF;
            border-left: 4px solid #3B82F6;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.25rem;
        }
        .mockup {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            overflow: hidden;
            margin: 1.5rem 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .mockup-header {
            background-color: #f8fafc;
            padding: 0.75rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
        }
        .mockup-title {
            font-weight: 600;
            margin-left: 0.5rem;
        }
        .mockup-content {
            padding: 1rem;
            background-color: white;
        }
        .color-swatch {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            border-radius: 0.25rem;
            margin-right: 0.5rem;
            vertical-align: middle;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container">
        <div class="section">
            <h1 class="text-3xl font-bold mb-6 text-center">聆花文化ERP系统</h1>
            <h2 class="text-2xl font-bold mb-4 text-center">高保真原型设计</h2>

            <h3 class="text-xl font-bold mt-8 mb-4">设计系统构建与风格定义</h3>

            <h4 class="text-lg font-bold mt-6 mb-2">设计风格</h4>
            <p>聆花文化ERP系统采用简洁、专业的设计风格，注重信息的清晰展示和操作的便捷性。设计风格具有以下特点：</p>
            <ul class="list-disc pl-6 mt-2">
                <li><strong>简洁明了</strong>：界面简洁，减少视觉干扰，突出核心功能</li>
                <li><strong>层次分明</strong>：信息层次清晰，重要信息突出显示</li>
                <li><strong>一致性强</strong>：统一的设计语言，提供一致的用户体验</li>
                <li><strong>专业可靠</strong>：体现企业级应用的专业性和可靠性</li>
                <li><strong>适度美观</strong>：在保证功能性的基础上，追求适度的美观</li>
            </ul>

            <h4 class="text-lg font-bold mt-6 mb-2">色彩系统</h4>
            <p>系统采用以下色彩方案：</p>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                <div class="p-4 rounded-lg bg-white shadow">
                    <div class="color-swatch" style="background-color: #3B82F6;"></div>
                    <span>主色调：#3B82F6</span>
                    <p class="text-sm text-gray-600 mt-1">用于主要按钮、重要信息高亮等</p>
                </div>
                <div class="p-4 rounded-lg bg-white shadow">
                    <div class="color-swatch" style="background-color: #10B981;"></div>
                    <span>成功色：#10B981</span>
                    <p class="text-sm text-gray-600 mt-1">用于成功状态、确认操作等</p>
                </div>
                <div class="p-4 rounded-lg bg-white shadow">
                    <div class="color-swatch" style="background-color: #F59E0B;"></div>
                    <span>警告色：#F59E0B</span>
                    <p class="text-sm text-gray-600 mt-1">用于警告信息、需要注意的操作等</p>
                </div>
                <div class="p-4 rounded-lg bg-white shadow">
                    <div class="color-swatch" style="background-color: #EF4444;"></div>
                    <span>错误色：#EF4444</span>
                    <p class="text-sm text-gray-600 mt-1">用于错误信息、危险操作等</p>
                </div>
            </div>

            <h4 class="text-lg font-bold mt-6 mb-2">排版系统</h4>
            <p>系统采用清晰的排版层级：</p>
            <div class="mt-4 p-6 bg-white rounded-lg shadow">
                <h1 class="text-3xl font-bold mb-4">一级标题 (24px)</h1>
                <h2 class="text-2xl font-bold mb-3">二级标题 (20px)</h2>
                <h3 class="text-xl font-bold mb-2">三级标题 (18px)</h3>
                <h4 class="text-lg font-bold mb-2">四级标题 (16px)</h4>
                <p class="text-base mb-2">正文文本 (14px)：清晰易读，适合长文本阅读。</p>
                <p class="text-sm text-gray-600">辅助文本 (12px)：用于次要信息、提示等。</p>
            </div>

            <h4 class="text-lg font-bold mt-6 mb-2">组件库</h4>
            <p>系统包含以下核心组件：</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                <div class="p-4 rounded-lg bg-white shadow">
                    <h5 class="font-bold mb-2">导航组件</h5>
                    <ul class="list-disc pl-6">
                        <li>顶部导航栏</li>
                        <li>侧边菜单</li>
                        <li>面包屑导航</li>
                        <li>标签页导航</li>
                    </ul>
                </div>
                <div class="p-4 rounded-lg bg-white shadow">
                    <h5 class="font-bold mb-2">表单组件</h5>
                    <ul class="list-disc pl-6">
                        <li>输入框</li>
                        <li>下拉选择器</li>
                        <li>日期选择器</li>
                        <li>单选/复选框</li>
                        <li>开关</li>
                    </ul>
                </div>
                <div class="p-4 rounded-lg bg-white shadow">
                    <h5 class="font-bold mb-2">数据展示组件</h5>
                    <ul class="list-disc pl-6">
                        <li>表格</li>
                        <li>列表</li>
                        <li>卡片</li>
                        <li>统计数值</li>
                        <li>图表</li>
                    </ul>
                </div>
                <div class="p-4 rounded-lg bg-white shadow">
                    <h5 class="font-bold mb-2">反馈组件</h5>
                    <ul class="list-disc pl-6">
                        <li>对话框</li>
                        <li>抽屉</li>
                        <li>通知提醒</li>
                        <li>进度条</li>
                        <li>加载中</li>
                    </ul>
                </div>
            </div>

            <h4 class="text-lg font-bold mt-6 mb-2">交互模式</h4>
            <p>系统采用以下核心交互模式：</p>
            <ul class="list-disc pl-6 mt-2">
                <li><strong>直接操作</strong>：用户可以直接操作界面元素，如拖拽、点击、滑动等</li>
                <li><strong>即时反馈</strong>：操作后立即给予反馈，如状态变化、提示信息等</li>
                <li><strong>渐进式展示</strong>：根据用户需求逐步展示信息，避免信息过载</li>
                <li><strong>上下文感知</strong>：根据当前上下文提供相关功能和信息</li>
                <li><strong>一致性交互</strong>：相似功能采用一致的交互方式，降低学习成本</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h3 class="text-xl font-bold mb-4">痛点驱动的原型设计与实现</h3>

            <h4 class="text-lg font-bold mt-6 mb-2">核心用户流程</h4>
            <p>基于对用户痛点的深入分析，我们设计了以下核心用户流程：</p>

            <div class="mermaid">
                graph TD
                    A[登录系统] --> B[进入首页]
                    B --> C1[产品管理]
                    B --> C2[库存管理]
                    B --> C3[销售管理]
                    B --> C4[员工管理]
                    B --> C5[渠道管理]
                    B --> C6[财务管理]
                    B --> C7[团建管理]
                    B --> C8[系统设置]

                    C1 --> D1[浏览产品]
                    D1 --> E1[创建/编辑产品]
                    D1 --> E2[管理产品分类]

                    C2 --> D2[查看库存]
                    D2 --> E3[入库操作]
                    D2 --> E4[出库操作]
                    D2 --> E5[库存调拨]

                    C3 --> D3[创建销售订单]
                    C3 --> D4[POS销售]
                    D3 --> E6[订单管理]
                    D4 --> E7[销售记录]

                    C4 --> D5[员工信息管理]
                    C4 --> D6[排班管理]
                    C4 --> D7[薪资计算]

                    C5 --> D8[渠道商管理]
                    D8 --> E8[渠道价格设置]
                    D8 --> E9[渠道库存管理]
                    D8 --> E10[渠道销售记录]

                    C6 --> D9[账户管理]
                    D9 --> E11[交易记录]
                    D9 --> E12[财务报表]

                    C7 --> D10[团建活动管理]
                    D10 --> E13[团建订单管理]

                    C8 --> D11[用户管理]
                    D11 --> E14[角色权限管理]
                    D11 --> E15[系统参数设置]
            </div>

            <h4 class="text-lg font-bold mt-6 mb-2">关键界面设计</h4>
            <p>以下是系统关键界面的高保真设计：</p>

            <div class="prototype-screen">
                <h5 class="font-bold mb-2">1. 登录界面</h5>
                <div class="mockup">
                    <div class="mockup-header">
                        <i class="fas fa-window-maximize"></i>
                        <span class="mockup-title">聆花文化ERP系统 - 登录</span>
                    </div>
                    <div class="mockup-content">
                        <div class="flex min-h-[400px]">
                            <div class="hidden md:flex md:w-1/2 bg-blue-50 items-center justify-center">
                                <div class="p-8 text-center">
                                    <i class="fas fa-gem text-6xl text-blue-500 mb-4"></i>
                                    <h2 class="text-2xl font-bold text-gray-800 mb-2">聆花文化ERP系统</h2>
                                    <p class="text-gray-600">非遗掐丝珐琅综合管理平台</p>
                                </div>
                            </div>
                            <div class="w-full md:w-1/2 flex items-center justify-center">
                                <div class="w-full max-w-md p-8">
                                    <div class="text-center mb-8">
                                        <i class="fas fa-user-circle text-5xl text-blue-500 mb-2"></i>
                                        <h2 class="text-2xl font-bold text-gray-800">系统登录</h2>
                                    </div>
                                    <form>
                                        <div class="mb-4">
                                            <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
                                                邮箱
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-envelope text-gray-400"></i>
                                                </div>
                                                <input class="shadow appearance-none border rounded w-full py-2 px-3 pl-10 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="email" type="email" placeholder="请输入邮箱">
                                            </div>
                                        </div>
                                        <div class="mb-6">
                                            <label class="block text-gray-700 text-sm font-bold mb-2" for="password">
                                                密码
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-lock text-gray-400"></i>
                                                </div>
                                                <input class="shadow appearance-none border rounded w-full py-2 px-3 pl-10 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="password" type="password" placeholder="请输入密码">
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between mb-6">
                                            <div>
                                                <input class="mr-2 leading-tight" type="checkbox" id="remember">
                                                <label class="text-sm text-gray-700" for="remember">
                                                    记住我
                                                </label>
                                            </div>
                                            <a class="inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800" href="#">
                                                忘记密码?
                                            </a>
                                        </div>
                                        <div class="mb-6">
                                            <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full" type="button">
                                                登录
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="design-feature mt-4">
                    <h5 class="font-bold">设计特点：</h5>
                    <ul class="list-disc pl-6">
                        <li>简洁明了的登录界面，减少干扰</li>
                        <li>清晰的表单结构，易于填写</li>
                        <li>响应式设计，适配不同设备</li>
                        <li>品牌元素展示，增强识别度</li>
                        <li>直观的图标提示，提升可用性</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
