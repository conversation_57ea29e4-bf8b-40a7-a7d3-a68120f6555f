"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  MenuIcon, XIcon, ChevronDownIcon, ChevronRightIcon,
} from "lucide-react"
import { navigationItems, NavItem } from "@/config/navigation"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
// 移除了重复的功能组件导入，这些功能现在在顶部导航栏中

interface EnhancedSidebarProps {
  isCollapsed?: boolean
  isOpen?: boolean
  onOpenChange?: (open: boolean) => void
}

export default function EnhancedSidebar({
  isCollapsed = false,
  isOpen = false,
  onOpenChange
}: EnhancedSidebarProps) {
  const pathname = usePathname()
  const router = useRouter()
  // 使用外部传入的状态，移除内部状态管理
  const setIsOpen = onOpenChange || (() => {})


  return (
    <>
      {/* 移动设备菜单按钮 - 移动到顶部导航栏下方 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="lg:hidden fixed top-20 left-4 z-50 p-2 rounded-md bg-background shadow-md"
        aria-label={isOpen ? "关闭菜单" : "打开菜单"}
      >
        {isOpen ? <XIcon size={20} /> : <MenuIcon size={20} />}
      </button>

      {/* 侧边栏 */}
      <div
        className={cn(
          "fixed left-0 z-30 bg-background shadow-lg transform transition-all duration-300 ease-in-out lg:translate-x-0 sidebar-modern",
          isOpen ? "translate-x-0" : "-translate-x-full",
          isCollapsed ? "w-[70px]" : "w-64",
          "top-16 h-[calc(100vh-4rem)]" // 从顶部导航栏下方开始，高度自适应
        )}
      >
        <div className="flex flex-col h-full">
          {/* 顶部区域 - 移除了收缩按钮，现在由顶部导航栏控制 */}
          <div className="border-b h-2"></div>

          {/* 导航菜单 - 优化滚动和高度 */}
          <ScrollArea className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            <nav className={cn(
              "py-4 space-y-1 min-h-full",
              isCollapsed ? "px-2" : "px-4"
            )}>
              {navigationItems.map((item) => {
                const ItemIcon = item.icon
                const isItemActive = pathname === item.href || pathname.startsWith(`${item.href}/`)

                return (
                  <div key={item.href}>
                    {isCollapsed ? (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Link
                              href={item.href}
                              className={cn(
                                "flex items-center justify-center h-10 w-10 rounded-md transition-colors",
                                isItemActive
                                  ? "bg-primary/10 text-primary"
                                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                              )}
                            >
                              <ItemIcon className="h-5 w-5" />
                            </Link>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            {item.title}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : (
                      <Link
                        href={item.href}
                        className={cn(
                          "flex items-center px-3 py-2 text-sm rounded-md transition-colors",
                          isItemActive
                            ? "bg-primary/10 text-primary font-medium"
                            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                        )}
                      >
                        <ItemIcon className="mr-3 h-4 w-4" />
                        <span>{item.title}</span>
                      </Link>
                    )}
                  </div>
                )
              })}


                                                {child.children.map(grandChild => {
                                                  const GrandChildIcon = grandChild.icon
                                                  const isGrandChildActive = pathname === grandChild.href || pathname.startsWith(`${grandChild.href}/`)

                                                  return (
                                                    <Link
                                                      key={grandChild.href}
                                                      href={grandChild.href}
                                                      className={cn(
                                                        "flex items-center pl-2 pr-3 py-1 text-xs rounded-md transition-colors",
                                                        isGrandChildActive
                                                          ? "bg-primary/10 text-primary font-medium"
                                                          : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                                                      )}
                                                      onClick={() => setIsOpen(false)}
                                                    >
                                                      <GrandChildIcon className="mr-2 h-3 w-3 flex-shrink-0" />
                                                      <span className="truncate">{grandChild.title}</span>
                                                    </Link>
                                                  )
                                                })}
                                              </div>
                                            </div>
                                          </div>
                                        )
                                      }

                                      // 二级菜单项（无子菜单）
                                      return (
                                        <Link
                                          key={child.href}
                                          href={child.href}
                                          className={cn(
                                            "flex items-center pl-4 pr-3 py-1.5 text-sm rounded-md transition-colors",
                                            isChildActive
                                              ? "bg-primary/10 text-primary font-medium"
                                              : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                                          )}
                                          onClick={() => setIsOpen(false)}
                                        >
                                          <ChildIcon className="mr-2 h-3 w-3 flex-shrink-0" />
                                          <span className="truncate">{child.title}</span>
                                        </Link>
                                      )
                                    })}
                                  </div>
                                </div>
                              </div>
                            )
                          }

                          // 没有子项，渲染为普通链接
                          return (
                            <Link
                              key={item.href}
                              href={item.href}
                              className={cn(
                                "flex items-center pl-10 pr-3 py-2 text-sm rounded-md transition-colors",
                                isItemActive
                                  ? "bg-primary/10 text-primary font-medium"
                                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                              )}
                              onClick={() => setIsOpen(false)}
                            >
                              <ItemIcon className="mr-3 h-4 w-4 flex-shrink-0" />
                              <span className="truncate">{item.title}</span>
                            </Link>
                          )
                        })}
                      </div>
                    )}

                    {/* 折叠模式下的导航项 */}
                    {isCollapsed && (
                      <div className="space-y-1">
                        {group.items.map(item => {
                          const ItemIcon = item.icon
                          const isItemActive = pathname === item.href || pathname.startsWith(`${item.href}/`)
                          const hasChildren = item.children && item.children.length > 0

                          // 在折叠模式下，即使有子项也只显示父项
                          return (
                            <TooltipProvider key={item.href}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Link
                                    href={item.href}
                                    className={cn(
                                      "flex items-center justify-center h-10 w-10 rounded-md mx-auto",
                                      isItemActive
                                        ? "bg-primary/10 text-primary"
                                        : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                                    )}
                                    onClick={() => setIsOpen(false)}
                                  >
                                    <ItemIcon className="h-5 w-5" />
                                    {hasChildren && (
                                      <div className="absolute -right-1 -bottom-1 h-2 w-2 rounded-full bg-primary" />
                                    )}
                                  </Link>
                                </TooltipTrigger>
                                <TooltipContent side="right">
                                  {item.title}
                                  {hasChildren && " (包含子菜单)"}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )
                        })}
                      </div>
                    )}
                  </div>
                )
              })}
            </nav>
          </ScrollArea>

          {/* 底部留空 - 所有功能已移至顶部导航栏 */}
        </div>
      </div>
    </>
  )
}
