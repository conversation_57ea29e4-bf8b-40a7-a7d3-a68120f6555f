# 聆花掐丝珐琅馆ERP系统 - 页面导航结构

## 🏠 主页面结构

### 1. 首页 `/`
- **路径**: `/`
- **组件**: `app/page.tsx`
- **功能**: 系统首页，显示登录界面或重定向到仪表板

### 2. 仪表板 `/dashboard`
- **路径**: `/dashboard`
- **组件**: `app/(main)/dashboard/page.tsx`
- **功能**: 系统主仪表板，显示关键业务指标和快速操作

## 📊 业务模块

### 3. 产品管理 `/products`
- **主页**: `/products` - 产品列表和管理
- **详情页**: `/products/[id]` - 产品详情页面
- **新增**: `/products/new` - 新增产品
- **编辑**: `/products/[id]/edit` - 编辑产品

### 4. 销售管理 `/sales`
- **主页**: `/sales` - 销售管理主页
- **标签页**:
  - POS销售 - 现场销售系统
  - 订单管理 - 销售订单管理
  - 客户管理 - 客户信息管理
  - 销售报表 - 销售数据分析

### 5. 库存管理 `/inventory`
- **主页**: `/inventory` - 库存概览
- **入库管理**: `/inventory/inbound` - 商品入库记录和操作
- **出库管理**: `/inventory/outbound` - 商品出库记录和操作
- **调拨管理**: `/inventory/transfer` - 仓库间库存调拨
- **盘点管理**: `/inventory/stocktake` - 库存盘点和差异处理

### 6. 采购管理 `/purchase`
- **主页**: `/purchase` - 采购管理主页
- **功能**: 采购订单、供应商管理、采购分析

### 7. 员工管理 `/employees`
- **主页**: `/employees` - 员工信息管理
- **功能**: 员工档案、考勤、薪资、绩效管理

### 8. 财务管理 `/finance`
- **主页**: `/finance` - 财务管理主页
- **收款管理**: `/finance/incomes` - 收款记录和应收账款
- **付款管理**: `/finance/expenses` - 付款记录和应付账款
- **资金账户**: `/finance/accounts` - 银行账户和资金流水

### 9. 渠道管理 `/channels`
- **主页**: `/channels` - 渠道管理主页
- **标签页**:
  - 渠道商管理 - 渠道商信息
  - 渠道价格 - 特殊价格管理
  - 渠道库存 - 渠道库存管理
  - 押金管理 - 渠道押金管理
  - 渠道配货 - 配货管理
  - 渠道销售 - 渠道销售数据
  - 结算管理 - 定期结算

### 10. 手作团建 `/workshops`
- **主页**: `/workshops` - 手作团建管理
- **功能**: 团建活动、师资安排、成本核算

### 11. 咖啡店管理 `/coffee-shop`
- **主页**: `/coffee-shop` - 咖啡店管理主页
- **销售管理**: `/coffee-shop/sales` - 咖啡店销售记录
- **采购管理**: `/coffee-shop/purchase` - 咖啡店采购管理

## 📋 系统管理

### 12. 系统设置 `/settings`
- **主页**: `/settings` - 系统设置主页
- **用户管理**: `/settings/users` - 用户账户管理
- **角色权限**: `/settings/roles` - 角色和权限管理
- **系统参数**: `/settings/system` - 系统参数配置

### 13. 报表中心 `/reports`
- **主页**: `/reports` - 报表中心
- **销售报表**: `/reports/sales` - 销售数据报表
- **库存报表**: `/reports/inventory` - 库存分析报表
- **财务报表**: `/reports/finance` - 财务数据报表

### 14. 排班管理 `/schedule`
- **主页**: `/schedule` - 员工排班管理
- **功能**: 排班计划、考勤记录、班次管理

### 15. 通知中心 `/notifications`
- **主页**: `/notifications` - 系统通知中心
- **功能**: 通知查看、标记已读、通知设置

## 📱 移动端页面

### 16. 移动端主页 `/mobile`
- **主页**: `/mobile` - 移动端主页
- **标签页**:
  - 仪表板 - 移动端仪表板
  - 产品 - 移动端产品管理
  - 销售 - 移动端销售功能
  - 员工 - 移动端员工管理

### 17. 移动端员工管理 `/mobile/employees`
- **员工列表** - 员工信息查看
- **排班管理** - 移动端排班
- **薪资查询** - 薪资记录查看
- **绩效管理** - 绩效考核

## 🔧 管理员页面

### 18. 管理员设置 `/admin/settings`
- **用户管理**: `/admin/settings/users` - 高级用户管理
- **系统监控**: `/admin/settings/monitoring` - 系统监控
- **数据备份**: `/admin/settings/backup` - 数据备份恢复

## 🔐 认证页面

### 19. 登录页面 `/auth/signin`
- **功能**: 用户登录

### 20. 注册页面 `/auth/signup`
- **功能**: 用户注册（如果启用）

## 📊 数据状态

### ✅ 已完成页面
- 仪表板 `/dashboard`
- 产品管理 `/products`
- 销售管理 `/sales`
- 库存管理 `/inventory` 及子页面
- 采购管理 `/purchase`
- 员工管理 `/employees`
- 财务管理 `/finance` 及子页面
- 渠道管理 `/channels`
- 手作团建 `/workshops`
- 咖啡店管理 `/coffee-shop` 及子页面
- 系统设置 `/settings`
- 报表中心 `/reports`
- 排班管理 `/schedule`
- 通知中心 `/notifications`
- 移动端页面 `/mobile` 及子页面

### 🔧 需要完善的功能
- 部分组件的数据获取逻辑
- 表单提交和数据更新功能
- 权限控制和用户认证
- 移动端响应式优化

## 🎯 导航结构

### 主导航栏（桌面端）
1. 仪表板
2. 产品管理
3. 销售管理
4. 库存管理
5. 采购管理
6. 员工管理
7. 财务管理
8. 渠道管理
9. 手作团建
10. 咖啡店
11. 报表中心
12. 排班管理
13. 系统设置

### 移动端底部导航
1. 首页
2. 产品
3. 销售
4. 员工

## 📝 备注

- 所有页面都已创建并具备基本功能
- 大部分页面使用真实数据库数据
- 移动端适配已完成
- 导航栏在所有主要页面中正常显示
- 系统支持响应式设计，适配桌面端和移动端
