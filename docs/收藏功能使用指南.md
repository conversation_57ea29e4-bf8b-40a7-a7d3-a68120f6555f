# 聆花ERP系统 - 收藏功能使用指南

## 📋 功能概述

收藏功能是聆花ERP系统阶段2深度个性化功能的核心组件，允许用户收藏常用的页面、报表、搜索条件和操作流程，提高工作效率。

## 🎯 主要特性

### 1. 四种收藏类型
- **页面收藏** - 收藏常用业务页面
- **报表收藏** - 保存报表配置
- **搜索收藏** - 保存搜索条件
- **操作收藏** - 保存操作流程

### 2. 智能管理
- **分类管理** - 按业务模块分类
- **使用统计** - 记录访问次数和时间
- **智能排序** - 按使用频率自动排序
- **快速访问** - 顶部导航栏一键访问

### 3. 个性化体验
- **自定义描述** - 添加个人备注
- **灵活分类** - 支持8个业务分类
- **批量管理** - 支持批量操作
- **搜索筛选** - 快速查找收藏项

## 🚀 使用方法

### 添加收藏

#### 1. 页面收藏
在任意页面标题旁点击收藏按钮：
```tsx
<FavoriteButton
  type="page"
  title="产品管理"
  url="/products"
  icon="PackageIcon"
  category="product"
  description="管理产品信息、分类和库存"
/>
```

#### 2. 报表收藏
在报表页面保存当前配置：
```tsx
<FavoriteButton
  type="report"
  title="月度销售报表"
  category="sales"
  config={{
    fields: ['date', 'amount', 'customer'],
    filters: { period: 'monthly' },
    chartType: 'bar'
  }}
/>
```

#### 3. 搜索收藏
保存复杂的搜索条件：
```tsx
<FavoriteButton
  type="search"
  title="高价值客户搜索"
  category="customer"
  config={{
    filters: { totalSpent: { min: 10000 } },
    sortBy: 'totalSpent'
  }}
/>
```

#### 4. 操作收藏
保存操作流程：
```tsx
<FavoriteButton
  type="operation"
  title="月末库存盘点"
  category="inventory"
  config={{
    steps: [
      'export_current_inventory',
      'physical_count',
      'compare_differences'
    ]
  }}
/>
```

### 管理收藏

#### 1. 快速访问
- 点击顶部导航栏的"收藏"按钮
- 查看最常用的收藏项（按访问频率排序）
- 一键打开收藏的页面或应用配置

#### 2. 收藏管理器
- 点击"管理收藏"打开完整管理界面
- 支持搜索、筛选、排序功能
- 可以编辑、删除收藏项

#### 3. 分类筛选
支持按以下分类筛选：
- 产品管理 (product)
- 销售管理 (sales)
- 库存管理 (inventory)
- 财务管理 (finance)
- 员工管理 (employee)
- 客户管理 (customer)
- 渠道管理 (channel)
- 系统设置 (system)
- 其他 (other)

## 🛠️ 技术实现

### 数据模型
```typescript
interface UserFavorite {
  id: string
  type: 'page' | 'report' | 'search' | 'operation'
  title: string
  url?: string
  icon?: string
  category?: string
  description?: string
  config?: any
  sortOrder: number
  accessCount: number
  lastAccess?: Date
}
```

### 核心组件

#### 1. PersonalizationProvider
提供收藏功能的上下文和状态管理：
```tsx
import { PersonalizationProvider } from '@/components/personalization/personalization-provider'

// 在应用根组件中包装
<PersonalizationProvider>
  <App />
</PersonalizationProvider>
```

#### 2. FavoriteButton
用于添加收藏的按钮组件：
```tsx
import { FavoriteButton } from '@/components/personalization/favorite-button'

<FavoriteButton
  type="page"
  title="页面标题"
  url="/page-url"
  category="business-category"
  description="页面描述"
/>
```

#### 3. FavoritesQuickAccess
顶部导航栏的快速访问组件：
```tsx
import { FavoritesQuickAccess } from '@/components/personalization/favorites-quick-access'

<FavoritesQuickAccess maxItems={6} />
```

#### 4. FavoritesManager
完整的收藏管理界面：
```tsx
import { FavoritesManager } from '@/components/personalization/favorites-manager'

<FavoritesManager />
```

### API接口

#### 1. 获取收藏列表
```
GET /api/personalization/favorites
Query: ?type=page&category=product
```

#### 2. 添加收藏
```
POST /api/personalization/favorites
Body: { type, title, url, icon, category, description, config }
```

#### 3. 更新收藏
```
PATCH /api/personalization/favorites/{id}
Body: { title, description, category, ... }
```

#### 4. 删除收藏
```
DELETE /api/personalization/favorites/{id}
```

#### 5. 记录访问
```
POST /api/personalization/favorites/{id}/access
```

## 📊 使用统计

### 访问记录
- 每次点击收藏项都会记录访问
- 统计访问次数和最近访问时间
- 用于智能排序和推荐

### 排序规则
1. **使用频率优先** - 访问次数多的排在前面
2. **最近访问优先** - 最近使用的排在前面
3. **创建时间** - 新创建的排在后面

## 🎨 界面设计

### 视觉元素
- **收藏按钮** - 星形图标，支持已收藏/未收藏状态
- **类型图标** - 不同收藏类型使用不同图标
- **分类标签** - 彩色标签区分业务分类
- **访问统计** - 显示访问次数和时间

### 交互体验
- **一键收藏** - 点击即可添加收藏
- **快速访问** - 下拉菜单快速选择
- **拖拽排序** - 支持手动调整顺序
- **批量操作** - 支持批量删除和分类

## 🔧 配置选项

### 显示设置
- `maxItems` - 快速访问显示的最大项目数
- `showManageButton` - 是否显示管理按钮
- `sortBy` - 默认排序方式
- `groupByCategory` - 是否按分类分组

### 权限控制
- 用户只能管理自己的收藏
- 支持团队共享收藏（未来功能）
- 管理员可以查看使用统计

## 📈 性能优化

### 缓存策略
- 本地缓存常用收藏项
- 懒加载收藏管理界面
- 防抖搜索和筛选

### 数据同步
- 实时更新访问统计
- 跨标签页状态同步
- 离线缓存支持

## 🚀 未来规划

### 阶段3功能
- **智能推荐** - AI推荐相关收藏
- **团队共享** - 部门级收藏共享
- **快捷键** - 键盘快捷键支持
- **导入导出** - 收藏数据备份

### 长期愿景
- **跨设备同步** - 多设备收藏同步
- **使用分析** - 详细的使用分析报告
- **个性化推荐** - 基于使用习惯的智能推荐

---

**版本：** v1.0  
**更新时间：** 2024年12月20日  
**状态：** ✅ 已完成实施
