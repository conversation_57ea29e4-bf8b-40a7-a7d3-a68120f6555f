## 聆花掐丝珐琅馆ERP系统 - 系统重构优化方案


本方案在AI助手提供的“总体规划”基础上，进行**具体优化和调整**，旨在使系统更符合您作为非遗掐丝珐琅品牌销售运营企业的实际需求，提升用户体验和操作效率。


### 一、系统架构概述 (保留并确认)


* **1.1 系统分层**：保持不变，这是标准且合理的架构。
* **1.2 技术栈**：保持不变，Next.js、React、Tailwind CSS、PostgreSQL、Prisma ORM 是现代且高效的技术栈。


### 二、页面结构重组 (核心优化)


我们将重新组织路由和模块，使其更符合业务逻辑和用户习惯。


#### 2.1 路由组织 (优化命名与分组)


在AI助手提供的路由组基础上，进行以下调整：


1.  **`(auth)`** - 认证相关页面 (如 `/login`, `/forgot-password`)
2.  **`(main)`** - **主要业务功能页面**（带侧边栏导航）
    * *说明：将原 `(dashboard)` 组更名为 `(main)`，因为该组将包含所有核心业务模块，不仅仅是仪表盘。*
3.  **`(mobile)`** - 移动端优化页面
4.  **`(fullscreen)`** - **全屏页面**（无侧边栏，如详细报表、某些详情页、操作向导）
    * *说明：将原 `(no-sidebar)` 更名为 `(fullscreen)`，更具描述性。*
5.  **`(admin)`** - 管理员专用页面 (如系统设置、用户权限管理)


#### 2.2 核心业务模块 (重新分类与整合)


根据您的业务流程和我们之前的讨论，重新划分核心业务模块，使其更直观：


1.  **概览与客户 (Overview & Customers)**
    * **仪表盘** (`/`) - 系统首页，显示关键业务指标。
    * **客户管理** (`/customers`) - 客户档案、互动记录。
2.  **销售与渠道 (Sales & Channels)**
    * **销售管理** (`/sales`) - 销售订单、POS销售、定制作品。
    * **渠道管理** (`/channels`) - 渠道商档案、价格、押金、出库、结算。
3.  **运营与服务 (Operations & Services)**
    * **手作团建** (`/workshops`) - 团建活动管理。
    * **咖啡店** (`/coffee-shop`) - 咖啡店销售与采购管理。
4.  **供应链与生产 (Supply Chain & Production)**
    * **产品管理** (`/products`) - 产品信息。
    * **采购管理** (`/purchase`) - 采购订单。
    * **库存管理** (`/inventory`) - 库存状态、入库、出库、调拨、盘点。
    * **制作管理** (`/production`) - 产品制作流程。
5.  **财务与人事 (Finance & HR)**
    * **财务管理** (`/finance`) - 收款、付款、资金账户。
    * **薪酬管理** (`/payroll`) - 薪资计算与发放。
    * **员工管理** (`/employees`) - 员工信息。
    * **排班管理** (`/schedules`) - 员工排班。
6.  **报表中心 (Reports Center)**
    * **综合报表** (`/reports`) - 各类业务综合分析。
    * **销售报表** (`/reports/sales`)
    * **采购报表** (`/reports/purchase`)
    * **库存报表** (`/reports/inventory`)
    * **财务报表** (`/reports/finance`)
    * **团建报表** (`/reports/workshops`)
    * **咖啡店报表** (`/reports/coffee-shop`)
7.  **系统管理 (System Administration)**
    * **系统设置** (`/settings`) - 通用参数、用户、角色、权限、数据导入导出、日志、监控等。


#### 2.3 页面命名规范 (确认并细化)


* 列表页：`/[模块名]` 或 `/[模块名]s` (复数，如 `/customers`, `/workshops`, `/schedules`)
* 详情页：`/[模块名]/[id]`
* 新增页：`/[模块名]/new`
* 编辑页：`/[模块名]/[id]/edit`
* 子功能页：`/[模块名]/[子功能名]`
* **移动端页面**：`/m/[功能路径]` (保持AI助手建议)


#### 2.4 详细页面结构 (整合与精简)


* **认证相关页面**：
    * `/login`
    * `/forgot-password`
    * `/reset-password`
    * `/unauthorized`
    * *移除：`/register` (ERP内部员工账号由管理员创建)*
* **仪表盘与客户**：
    * `/` - **系统首页/仪表盘**
    * `/customers` - 客户列表
    * `/customers/new` - 新增客户
    * `/customers/[id]` - 客户详情 (整合原 `/employees/[id]/performance` 和 `/employees/[id]/salary` 中与客户相关的部分，以及 `/account` 中与客户相关的部分)
    * *移除：`/legacy-dashboard`*
* **销售与渠道**：
    * `/sales` - 销售订单总览
    * `/sales/pos` - POS销售记录
    * `/sales/orders` - 订单销售记录
    * `/sales/custom-works` - 定制作品订单
    * `/sales/new` - 新建销售订单 (可选择类型)
    * `/sales/[id]` - 销售订单详情
    * `/channels` - 渠道商列表
    * `/channels/new` - 新增渠道商
    * `/channels/[id]` - 渠道商详情
* **运营与服务**：
    * `/workshops` - 手作团建活动列表 (原 `/workshop`)
    * `/workshops/new` - 新建团建活动
    * `/workshops/[id]` - 团建活动详情
    * `/coffee-shop` - 咖啡店管理主页 (可包含销售概览、库存概览)
    * `/coffee-shop/sales` - 咖啡店销售记录 (整合原 `/coffee-reports` 中的销售数据展示和 `/daily-log?tab=coffee` 的数据录入)
    * `/coffee-shop/purchase` - 咖啡店采购记录
* **供应链与生产**：
    * `/products` - 产品列表
    * `/products/new` - 新增产品
    * `/products/[id]` - 产品详情
    * *移除：`/products/analytics` (移至报表中心)*
    * `/purchase` - 采购订单列表
    * `/purchase/new` - 新建采购订单
    * `/purchase/[id]` - 采购订单详情
    * `/inventory` - 库存管理总览
    * `/inventory/inbound` - 入库管理
    * `/inventory/outbound` - 出库管理
    * `/inventory/transfer` - 调拨管理
    * `/inventory/stocktake` - 盘点管理
    * `/production` - 制作任务列表
    * `/production/new` - 新建制作任务
    * `/production/[id]` - 制作任务详情
* **财务与人事**：
    * `/finance` - 财务管理主页/收支总览
    * `/finance/incomes` - 收款管理
    * `/finance/expenses` - 付款管理
    * `/finance/accounts` - 资金账户管理
    * *移除：`/finance/categories` (收支分类应在系统设置中管理)*
    * *移除：`/finance/transactions` (由 `incomes` 和 `expenses` 列表覆盖)*
    * *移除：`/finance/reports` (移至报表中心)*
    * `/payroll` - 薪酬管理主页 (整合原 `/salary` 和 `/payroll`)
    * `/payroll/records` - 薪资记录列表 (整合原 `/salary/[id]`)
    * `/payroll/disbursements` - 薪资发放管理
    * `/employees` - 员工列表 (原 `/employees` 和 `/users` 合并)
    * `/employees/new` - 新增员工
    * `/employees/[id]` - 员工详情 (整合原 `/employees/[id]/performance` 和 `/employees/[id]/salary`)
    * `/schedules` - 排班管理 (原 `/schedule`)
* **报表中心**：
    * `/reports` - 综合报表入口
    * `/reports/sales` - 销售报表
    * `/reports/purchase` - 采购报表
    * `/reports/inventory` - 库存报表
    * `/reports/finance` - 财务报表
    * `/reports/workshops` - 团建报表
    * `/reports/coffee-shop` - 咖啡店报表 (原 `/coffee-reports`)
* **系统设置**：
    * `/settings` - 系统设置主页
    * `/settings/users` - 用户管理 (整合原 `/accounts`, `/settings/users`, `/admin/users`)
    * `/settings/roles` - 角色管理
    * `/settings/permissions` - 权限分配 (原 `/permissions`)
    * `/settings/data-io` - 数据导入导出 (整合原 `/settings/backup`, `/test/product-export` 等)
    * `/settings/logs` - 系统日志
    * `/settings/monitoring` - 系统监控 (整合原 `/settings/performance`, `/admin/monitoring`)
    * *移除：`/settings/parameters` (整合到 `/settings` 主页)*
    * *移除：`/accounts/profile`, `/accounts/security`, `/accounts/settings` (这些是个人账户设置，建议从顶部用户头像下拉菜单进入，不占主导航)*
* **通知与消息**：
    * `/notifications` - 通知中心 (建议从顶部导航栏入口进入)
    * *移除：`/legacy-notifications`*
* **工作流管理**：
    * `/workflows` - 工作流列表 (建议作为未来扩展，如果当前不急迫，可暂时不放入主导航)
    * `/workflows/[id]` - 工作流详情
    * `/workflows/approvals` - 审批管理
    * `/workflows/instances/[id]` - 工作流实例详情
    * `/workflows/my` - 我的工作流
* **其他页面**：
    * *移除：`/todos`, `/todos/new` (待办事项建议整合到首页仪表盘或通知中心)*
    * *移除：`/test/*` (所有测试页面不应出现在生产规划中)*


### 三、导航结构优化 (桌面端与移动端)


#### 3.1 桌面端导航 (侧边栏)


采用分组折叠式侧边栏设计，并进行以下优化：


1.  **概览与客户组**
    * 仪表盘 (`/`)
    * 客户管理 (`/customers`)
2.  **销售与渠道组**
    * 销售管理 (`/sales`)
        * POS销售记录 (`/sales/pos`)
        * 订单销售记录 (`/sales/orders`)
        * 定制作品 (`/sales/custom-works`)
    * 渠道管理 (`/channels`)
3.  **运营与服务组**
    * 手作团建 (`/workshops`)
    * 咖啡店 (`/coffee-shop`)
4.  **供应链与生产组**
    * 产品管理 (`/products`)
    * 采购管理 (`/purchase`)
    * 库存管理 (`/inventory`)
    * 制作管理 (`/production`)
5.  **财务与人事组**
    * 财务管理 (`/finance`)
        * 收款管理 (`/finance/incomes`)
        * 付款管理 (`/finance/expenses`)
        * 资金账户 (`/finance/accounts`)
    * 薪酬管理 (`/payroll`)
    * 员工管理 (`/employees`)
    * 排班管理 (`/schedules`)
6.  **报表中心组**
    * 综合报表 (`/reports`)
    * 销售报表 (`/reports/sales`)
    * 采购报表 (`/reports/purchase`)
    * 库存报表 (`/reports/inventory`)
    * 财务报表 (`/reports/finance`)
    * 团建报表 (`/reports/workshops`)
    * 咖啡店报表 (`/reports/coffee-shop`)
7.  **系统管理组**
    * 系统设置 (`/settings`)
        * 用户管理 (`/settings/users`)
        * 角色管理 (`/settings/roles`)
        * 权限分配 (`/settings/permissions`)
        * 数据导入导出 (`/settings/data-io`)
        * 系统日志 (`/settings/logs`)
        * 系统监控 (`/settings/monitoring`)


#### 3.2 移动端导航 (底部导航栏)


移动端采用底部导航栏设计，聚焦核心操作，并**整合“数据录入”**：


1.  **首页** (`/m/dashboard`) - 仪表盘和核心数据概览。
2.  **快速操作** (`/m/quick-actions`) - **整合“数据录入”的核心入口。**
    * *说明：这是一个专门为移动端设计的，包含常用数据录入的快捷入口，如“录入POS销售”、“录入收款”、“新增客户”、“扫码入库”等。用户点击此图标即可快速进入一个统一的录入界面，再选择具体要录入的业务类型。*
3.  **产品与库存** (`/m/products`) - 产品查询和库存管理。
4.  **销售** (`/m/sales`) - POS销售和订单管理。
5.  **我的** (`/m/profile`) - 个人相关功能和设置，包括个人资料、安全设置、**我的待办事项**、**我的快速录入**（如果“快速操作”页面内容过多，可将部分个性化录入放到这里）。


#### 3.3 导航交互优化 (确认并强调)


* **记忆状态**：记住用户上次展开/折叠的导航组。
* **上下文感知**：高亮显示当前页面对应的导航项。
* **快捷搜索**：提供全局搜索功能，快速定位页面。
* **最近访问**：显示用户最近访问的页面 (可在仪表盘或个人中心)。
* **个性化**：允许用户自定义常用功能 (可在个人设置中配置，或在仪表盘上拖拽卡片)。


### 四、移动端规划 (整合“数据录入”)


* **4.1 核心功能**：
    1.  产品和库存查询。
    2.  销售记录和POS操作。
    3.  **快速数据录入** (通过 `/m/quick-actions` 页面实现)。
    4.  通知和提醒。
    5.  个人工作相关功能。
* **4.2 离线功能**：保持不变。
* **4.3 移动端特有功能**：保持不变。


### 五、数据模型优化 (确认)


* **5.1 核心数据模型**：保持不变，这些是ERP的基础。
* **5.2 关系优化**：保持不变。
* **5.3 数据验证**：保持不变。


### 六、权限控制 (确认)


* **6.1 角色设计**：保持不变，这些角色设计合理。
* **6.2 权限粒度**：保持不变，细粒度权限控制是ERP的必要条件。
* **6.3 权限实现**：保持不变。


### 七、实施计划 (确认)


* **7.1 第一阶段：基础架构重构**：保持不变。
* **7.2 第二阶段：核心功能优化**：保持不变。
* **7.3 第三阶段：移动端开发**：保持不变。
* **7.4 第四阶段：系统集成与优化**：保持不变。


### 八、技术实现建议 (确认)


* **8.1 前端优化**：保持不变。
* **8.2 后端优化**：保持不变。
* **8.3 部署和运维**：保持不变。


---


**关于“数据录入”的优化方案总结：**


1.  **桌面端：** 不再作为侧边栏的独立顶级模块。
    * 在**首页仪表盘**上可以有一个“**快速操作**”或“**日常录入**”的卡片/区域，提供常用数据录入的快捷链接（例如：新建POS销售、录入收款、新建采购单等）。
    * 具体的业务数据录入仍在其所属模块内部（如销售模块的“新建销售订单”）。
2.  **移动端：** 在底部导航栏中增加一个“**快速操作**”或“**数据**”入口 (`/m/quick-actions`)。
    * 点击后进入一个**统一的快速录入页面**，该页面提供多个常用业务的录入表单或链接，例如：
        * POS销售快速录入
        * 收款快速录入
        * 采购快速录入
        * 库存入库/出库快速录入（扫码）
        * 咖啡店销售快速录入
    * 这样既满足了快速录入的需求，又避免了将其作为独立的模块，使其更符合移动端的操作习惯。


这个优化方案在保留AI助手技术优势的基础上，更侧重于您的业务流程和用户体验，特别是解决了“数据录入”的痛点。您觉得这个具体的修改方案怎么样？
