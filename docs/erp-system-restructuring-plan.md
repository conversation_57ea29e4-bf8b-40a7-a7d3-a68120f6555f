# 聆花掐丝珐琅馆ERP系统 - 系统重构总体规划

本文档提供了聆花掐丝珐琅馆ERP系统的总体重构规划，整合了页面结构、导航设计、移动端规划等多个方面的内容。

## 一、系统架构概述

### 1.1 系统分层

聆花掐丝珐琅馆ERP系统采用以下分层架构：

1. **表示层**：用户界面和交互
   - 桌面Web界面
   - 移动端PWA界面
   - 响应式组件

2. **应用层**：业务逻辑和流程
   - 服务端组件和操作
   - 工作流引擎
   - 权限控制

3. **数据层**：数据存储和访问
   - Prisma ORM
   - 数据库模型
   - 数据验证和转换

### 1.2 技术栈

- **前端**：Next.js 13+, React, Tailwind CSS, shadcn/ui
- **后端**：Next.js API Routes, Server Actions
- **数据库**：PostgreSQL, Prisma ORM
- **部署**：Docker, Vercel/自托管服务器

## 二、页面结构重组

### 2.1 路由组织

系统页面按照以下路由组结构进行组织：

1. **`(auth)`** - 认证相关页面
2. **`(dashboard)`** - 主要业务功能页面（带侧边栏导航）
3. **`(mobile)`** - 移动端优化页面
4. **`(no-sidebar)`** - 无侧边栏的全屏页面（如报表、详情页）
5. **`(admin)`** - 管理员专用页面

### 2.2 核心业务模块

系统包含以下核心业务模块：

1. **仪表盘与概览** - 系统首页、数据录入、通知中心
2. **人员管理** - 员工管理、排班管理、薪资管理
3. **商品与库存** - 产品管理、库存管理、采购管理、制作管理、手作团建管理
4. **销售与渠道** - 销售管理、POS销售、渠道管理
5. **财务管理** - 财务概览、资金账户、收支分类、交易记录、财务报表
6. **报表系统** - 综合报表、咖啡店报表、各类业务报表
7. **系统管理** - 系统设置、账号管理、权限管理、工作流管理、数据备份

### 2.3 页面命名规范

- 列表页：`/[模块名]`
- 详情页：`/[模块名]/[id]`
- 新增页：`/[模块名]/new`
- 编辑页：`/[模块名]/[id]/edit`
- 子功能页：`/[模块名]/[功能名]`
- 移动端页面：`/m/[功能路径]`

## 三、导航结构优化

### 3.1 桌面端导航

桌面端导航采用分组折叠式侧边栏设计：

1. **概览组**
   - 仪表盘
   - 数据录入

2. **人员管理组**
   - 排班管理
   - 员工管理
   - 薪资管理

3. **商品与库存组**
   - 产品管理
   - 库存管理
   - 采购管理
   - 制作管理
   - 手作团建管理

4. **销售与渠道组**
   - 销售管理
   - POS销售记录
   - 渠道管理

5. **财务管理组**
   - 财务概览
   - 资金账户
   - 收支分类
   - 交易记录
   - 财务报表

6. **报表组**
   - 综合报表
   - 咖啡店报表

7. **系统组**
   - 系统设置
   - 账号管理
   - 权限管理
   - 工作流管理
   - 数据备份

### 3.2 移动端导航

移动端采用底部导航栏设计，包含5个核心功能入口：

1. **首页** - 仪表盘和快捷功能
2. **产品** - 产品查询和库存管理
3. **销售** - POS销售和订单管理
4. **数据** - 各类数据录入
5. **我的** - 个人相关功能和设置

### 3.3 导航交互优化

- **记忆状态**：记住用户上次展开/折叠的导航组
- **上下文感知**：高亮显示当前页面对应的导航项
- **快捷搜索**：提供全局搜索功能，快速定位页面
- **最近访问**：显示用户最近访问的页面
- **个性化**：允许用户自定义常用功能

## 四、移动端规划

### 4.1 核心功能

移动端专注于以下核心功能：

1. **产品和库存查询**
2. **销售记录和POS操作**
3. **简单的数据录入**
4. **通知和提醒**
5. **个人工作相关功能**

### 4.2 离线功能

移动端支持以下离线功能：

1. **产品和库存查询**
2. **销售记录**
3. **数据录入**
4. **文档和帮助**

### 4.3 移动端特有功能

1. **扫码功能**
2. **地理位置功能**
3. **相机集成**
4. **推送通知**

## 五、数据模型优化

### 5.1 核心数据模型

优化以下核心数据模型：

1. **User** - 用户账号
2. **Employee** - 员工信息
3. **Product** - 产品信息
4. **Inventory** - 库存信息
5. **Purchase** - 采购订单
6. **Sale** - 销售订单
7. **Channel** - 渠道合作伙伴
8. **Finance** - 财务记录
9. **Workshop** - 手作团建

### 5.2 关系优化

- 优化多对多关系设计
- 规范化外键命名
- 添加必要的索引
- 实现软删除机制

### 5.3 数据验证

- 统一的数据验证机制
- 前后端一致的验证规则
- 详细的错误提示

## 六、权限控制

### 6.1 角色设计

系统包含以下基本角色：

1. **超级管理员** - 完全访问权限
2. **管理员** - 管理系统设置和用户
3. **财务人员** - 管理财务相关功能
4. **销售人员** - 管理销售和客户
5. **库存管理员** - 管理产品和库存
6. **生产人员** - 管理生产和制作
7. **普通员工** - 基本功能访问

### 6.2 权限粒度

权限控制粒度分为以下几个层次：

1. **页面级权限** - 控制页面访问
2. **功能级权限** - 控制功能操作
3. **数据级权限** - 控制数据访问范围
4. **字段级权限** - 控制字段可见性和编辑权限

### 6.3 权限实现

- 基于中间件的路由权限控制
- 基于组件的UI权限控制
- 基于服务端操作的数据权限控制

## 七、实施计划

### 7.1 第一阶段：基础架构重构（1-2个月）

1. 路由组织结构调整
2. 导航栏重新设计
3. 权限控制框架实现
4. 数据模型优化

### 7.2 第二阶段：核心功能优化（2-3个月）

1. 产品和库存管理优化
2. 销售和POS系统完善
3. 财务管理系统完善
4. 报表系统重构

### 7.3 第三阶段：移动端开发（3-4个月）

1. 移动端基础架构搭建
2. 核心功能移动适配
3. 离线功能实现
4. 移动特有功能开发

### 7.4 第四阶段：系统集成与优化（1-2个月）

1. 系统性能优化
2. 用户体验提升
3. 数据迁移和集成
4. 系统测试和部署

## 八、技术实现建议

### 8.1 前端优化

1. **组件设计**：
   - 采用原子设计理念
   - 构建可复用组件库
   - 实现响应式设计

2. **状态管理**：
   - 使用React Context和Hooks
   - 实现数据缓存和预取
   - 优化重渲染性能

3. **用户体验**：
   - 实现平滑过渡和动画
   - 优化加载性能
   - 提供清晰的反馈机制

### 8.2 后端优化

1. **API设计**：
   - RESTful API设计
   - 统一的错误处理
   - 请求验证和安全控制

2. **数据处理**：
   - 批量操作优化
   - 数据缓存策略
   - 异步处理大量数据

3. **安全性**：
   - 输入验证和消毒
   - CSRF和XSS防护
   - 敏感数据加密

### 8.3 部署和运维

1. **容器化**：
   - Docker容器部署
   - 环境一致性保证
   - 简化部署流程

2. **监控**：
   - 性能监控
   - 错误跟踪
   - 用户行为分析

3. **备份和恢复**：
   - 自动化备份策略
   - 快速恢复机制
   - 数据完整性验证
