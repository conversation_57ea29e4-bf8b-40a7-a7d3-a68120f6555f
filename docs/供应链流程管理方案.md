# 聆花掐丝珐琅馆 - 供应链流程管理方案

## 一、供应链流程概述

### 当前业务流程
1. **产品设计** → 2. **底胎采购/定制** → 3. **寄送广西生产基地** → 4. **工艺制作** → 5. **寄回广州** → 6. **配饰装裱包装** → 7. **渠道销售** → 8. **月度结款**

### 流程特点
- **地理分离**：设计在广州，生产在广西，最终包装在广州
- **财务独立**：广西生产基地财务单独核算
- **质量控制**：需要严格的质量检验和追溯
- **物流管理**：涉及多次跨省运输
- **渠道结算**：按月进行销售结款

## 二、ERP系统调整方案

### 2.1 新增数据模型

#### 生产基地管理 (ProductionBase)
```typescript
interface ProductionBase {
  id: number;
  name: string;              // 基地名称
  code: string;              // 基地编码
  location: string;          // 所在地区
  contactName?: string;      // 联系人
  contactPhone?: string;     // 联系电话
  contactEmail?: string;     // 联系邮箱
  address?: string;          // 详细地址
  specialties: string[];     // 专长工艺类型
  capacity?: number;         // 月产能
  leadTime?: number;         // 标准制作周期(天)
  qualityRating?: number;    // 质量评级
  isActive: boolean;         // 是否活跃
  notes?: string;            // 备注
}
```

#### 生产订单管理 (ProductionOrder)
```typescript
interface ProductionOrder {
  id: number;
  orderNumber: string;       // 生产订单号
  productionBaseId: number;  // 生产基地ID
  employeeId: number;        // 负责人ID
  sourceOrderId?: number;    // 来源销售订单ID
  orderDate: Date;           // 下单日期
  expectedStartDate?: Date;  // 预计开始日期
  expectedEndDate?: Date;    // 预计完成日期
  actualStartDate?: Date;    // 实际开始日期
  actualEndDate?: Date;      // 实际完成日期
  status: string;            // 订单状态
  priority: string;          // 优先级
  totalAmount: number;       // 订单总金额
  paidAmount: number;        // 已付金额
  paymentStatus: string;     // 付款状态
  shippingMethod?: string;   // 寄送方式
  trackingNumber?: string;   // 快递单号
  notes?: string;            // 备注
}
```

#### 质量检验记录 (QualityRecord)
```typescript
interface QualityRecord {
  id: number;
  productionOrderId?: number; // 生产订单ID
  productionBaseId: number;   // 生产基地ID
  productId: number;          // 产品ID
  inspectorId: number;        // 检验员ID
  inspectionDate: Date;       // 检验日期
  qualityGrade: string;       // 质量等级 (A/B/C/D)
  qualityScore?: number;      // 质量评分
  defectDescription?: string; // 缺陷描述
  actionRequired?: string;    // 需要采取的行动
  status: string;             // 检验状态
  images: string[];           // 检验照片
  notes?: string;             // 备注
}
```

#### 物流记录 (ShippingRecord)
```typescript
interface ShippingRecord {
  id: number;
  productionOrderId: number;  // 生产订单ID
  shippingType: string;       // 物流类型 (to_production/from_production)
  shippingDate: Date;         // 发货日期
  expectedDate?: Date;        // 预计到达日期
  actualDate?: Date;          // 实际到达日期
  carrier?: string;           // 承运商
  trackingNumber?: string;    // 快递单号
  shippingCost?: number;      // 运费
  status: string;             // 物流状态
  notes?: string;             // 备注
}
```

### 2.2 业务流程状态管理

#### 生产订单状态流转
```
pending (待确认) 
  ↓
confirmed (已确认) 
  ↓
in_production (生产中) 
  ↓
quality_check (质检中) 
  ↓
completed (已完成) 
  ↓
shipped (已发货) 
  ↓
[到达广州] → 进入库存管理
```

#### 质量检验状态
```
pending (待检验) → approved (通过) → 进入下一环节
                → rejected (不合格) → 返工或报废
                → rework (需返工) → 重新生产
```

#### 物流状态
```
pending (待发货) → shipped (已发货) → in_transit (运输中) → delivered (已送达) → exception (异常)
```

### 2.3 财务管理调整

#### 生产成本核算
- **材料成本**：底胎采购成本
- **加工费用**：支付给生产基地的加工费
- **物流费用**：往返运输费用
- **质检费用**：质量检验相关费用
- **包装费用**：最终包装材料和人工费用

#### 付款管理
- **预付款**：下单时支付的定金
- **进度款**：根据生产进度支付
- **尾款**：质检合格后支付
- **运费**：物流费用结算

### 2.4 库存管理调整

#### 多仓库管理
```
广州总仓 (主仓库)
├── 原材料区 (底胎等)
├── 半成品区 (待发往生产基地)
├── 成品区 (生产完成待包装)
└── 包装成品区 (最终产品)

广西生产基地仓库 (虚拟仓库)
├── 待加工区
├── 生产中区
└── 完工待发区
```

#### 库存状态追踪
- **原材料**：采购入库的底胎
- **在途库存**：发往生产基地的材料
- **生产中库存**：正在加工的产品
- **质检库存**：完工待检验的产品
- **成品库存**：质检合格的成品
- **包装成品**：最终包装完成的产品

## 三、系统功能模块

### 3.1 生产基地管理
- **基地档案**：基本信息、联系方式、专长工艺
- **能力评估**：产能、交期、质量评级
- **合作历史**：历史订单、质量记录、合作评价
- **绩效分析**：按时交付率、质量合格率、成本控制

### 3.2 生产订单管理
- **订单创建**：从销售订单转换或独立创建
- **进度跟踪**：实时更新生产状态
- **质量管控**：质检记录和问题追踪
- **成本核算**：各环节成本统计

### 3.3 物流管理
- **发货管理**：发往生产基地的物流
- **收货管理**：从生产基地返回的物流
- **运费核算**：物流成本统计
- **异常处理**：物流异常和处理记录

### 3.4 质量管理
- **检验标准**：制定各类产品的质量标准
- **检验记录**：详细的质检数据和照片
- **问题追溯**：质量问题的原因分析和改进
- **供应商评级**：基于质量表现的基地评级

### 3.5 财务管理
- **成本核算**：按订单核算完整成本
- **付款管理**：多阶段付款流程
- **对账管理**：与生产基地的定期对账
- **利润分析**：各环节的利润贡献分析

## 四、报表和分析

### 4.1 生产报表
- **生产进度报表**：各订单的生产状态
- **产能利用报表**：各基地的产能使用情况
- **交期分析报表**：按时交付率统计
- **质量分析报表**：质量问题统计和趋势

### 4.2 成本分析
- **成本构成分析**：各环节成本占比
- **基地成本对比**：不同基地的成本效率
- **运输成本分析**：物流费用趋势
- **质量成本分析**：质量问题导致的额外成本

### 4.3 供应商评估
- **综合评分**：质量、交期、成本综合评价
- **趋势分析**：各基地表现的变化趋势
- **风险评估**：供应风险识别和预警

## 五、实施建议

### 5.1 分阶段实施
1. **第一阶段**：建立基础数据模型和生产基地管理
2. **第二阶段**：实现生产订单管理和状态跟踪
3. **第三阶段**：完善质量管理和物流管理
4. **第四阶段**：优化财务核算和报表分析

### 5.2 数据迁移
- **历史订单数据**：将现有生产记录迁移到新系统
- **基地信息**：录入现有合作基地的详细信息
- **质量标准**：建立产品质量检验标准库

### 5.3 培训和推广
- **操作培训**：针对不同角色的系统使用培训
- **流程培训**：新业务流程的培训和推广
- **持续改进**：根据使用反馈持续优化系统

这个方案将帮助您的ERP系统更好地支持复杂的供应链流程，提高生产管理的效率和透明度。
