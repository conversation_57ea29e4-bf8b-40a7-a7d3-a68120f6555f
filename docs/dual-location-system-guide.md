# 聆花珐琅双地点生产流程库存自动化管理和成本核算系统使用指南

## 📋 系统概述

聆花珐琅双地点生产流程库存自动化管理和成本核算系统是专为聆花文化珐琅工艺品生产设计的智能化管理系统，支持广州设计中心和广西生产基地的协同作业，实现库存自动转移、成本精确核算和薪酬智能管理。

### 🎯 核心功能

1. **双地点库存自动化管理** - 广州↔广西库存自动转移
2. **成本核算与分析** - 精确的生产成本记录和分析
3. **计件工资管理** - 自动化的计件工资计算和审核
4. **生产流程自动化** - 8阶段生产流程的智能管理
5. **实时数据同步** - 跨地点的实时数据同步

## 🚀 快速开始

### 1. 系统登录

使用以下管理员账号登录系统：
- **用户名**: <EMAIL>
- **密码**: Admin123456

### 2. 访问生产管理模块

登录后，点击左侧导航栏的"生产/供应链管理"进入主模块。

### 3. 选择功能标签页

系统提供6个主要功能标签页：
- **生产订单** - 智能生产订单管理
- **生产基地** - 生产基地管理
- **计件工单** - 计件工单管理
- **制作报表** - 生产数据分析
- **库存自动化** - 双地点库存自动化管理 ⭐
- **成本核算** - 成本核算与薪酬管理 ⭐

## 📦 库存自动化管理

### 功能概述

库存自动化管理模块支持广州设计中心和广西生产基地之间的智能库存转移，确保生产流程的顺畅进行。

### 主要功能

#### 1. 库存转移记录

**访问路径**: 生产管理 → 库存自动化 → 库存转移

**功能说明**:
- 查看所有库存转移记录
- 支持按状态、类型、仓库筛选
- 实时跟踪转移状态和物流信息

**转移类型**:
- 🔵 **原料发往生产** - 广州→广西：底胎调配到生产基地
- 🟢 **半成品返回** - 广西→广州：半成品返回精加工
- 🟣 **成品转移** - 精加工完成后成品入库
- 🟡 **质检转移** - 质量检验相关转移
- 🔴 **紧急调拨** - 紧急情况下的库存调拨

#### 2. 自动化规则配置

**访问路径**: 生产管理 → 库存自动化 → 自动化规则

**功能说明**:
- 配置生产阶段变更时的自动转移规则
- 设置触发条件和转移参数
- 监控规则执行情况和统计

**规则配置示例**:
```
规则名称: 生产阶段自动转移
触发事件: 阶段完成
源阶段: 采购完成
目标阶段: 发往生产
转移类型: 原料发往生产
源仓库: 广州原料仓
目标仓库: 广西生产仓
```

#### 3. 生产流程可视化

**访问路径**: 生产管理 → 库存自动化 → 生产流程

**流程阶段**:

1. **广州总部阶段**
   - 销售订单 → 生产计划 → 底胎准备

2. **物流发送阶段**
   - 广州 → 广西：底胎调配到生产基地

3. **广西生产基地阶段**
   - 原料入库 → 工艺制作 → 质量检验

4. **物流返回阶段**
   - 广西 → 广州：半成品返回精加工

5. **广州后处理阶段**
   - 点蓝工艺 → 配饰装裱 → 成品入库

#### 4. 成本分析

**访问路径**: 生产管理 → 库存自动化 → 成本分析

**分析维度**:
- **成本构成**: 直接材料45%、直接人工30%、物流运输15%、管理费用10%
- **地点分布**: 广州总部60%、广西生产基地25%、物流运输15%

## 💰 成本核算管理

### 功能概述

成本核算管理模块提供精确的生产成本记录、计件工资管理和薪酬自动计算功能。

### 主要功能

#### 1. 计件工资管理

**访问路径**: 生产管理 → 成本核算 → 计件工资

**工作类型**:
- 🔵 **点蓝工艺** (广州) - 按件计费的点蓝精加工工序
- 🟢 **配饰工艺** (广州) - 按件计费的配饰和装裱工序
- 🟣 **抛光工艺** (广西) - 半成品抛光处理
- 🟡 **组装工艺** (广西) - 产品组装工序
- 🟠 **包装工艺** (广州) - 成品包装工序
- ⚪ **质量检验** (广西) - 质量检验工序

**操作流程**:
1. 点击"新建记录"创建计件工资记录
2. 选择员工、工作类型、完成件数
3. 系统自动计算总金额（件数 × 单价 + 质量奖金 - 质量扣款）
4. 提交审核，等待管理员审批
5. 审核通过后进入支付流程

**批量操作**:
- 选择多条记录进行批量审核
- 批量支付已审核的工资记录
- 批量拒绝不符合要求的记录

#### 2. 生产成本记录

**访问路径**: 生产管理 → 成本核算 → 生产成本

**成本类别**:
- **直接材料** - 原材料、底胎、珐琅釉料等
- **直接人工** - 工艺制作、精加工人工成本
- **制造费用** - 设备折旧、水电费等
- **物流费用** - 双地点运输成本
- **质量成本** - 质量检验、返工成本

**成本记录流程**:
1. 选择关联的生产订单
2. 选择成本类别和发生阶段
3. 输入成本金额和相关描述
4. 系统自动关联到对应的生产订单

#### 3. 成本分析报表

**访问路径**: 生产管理 → 成本核算 → 成本分析

**分析功能**:
- **利润分析** - 总收入、总成本、毛利润、利润率
- **成本分类** - 按类别、阶段、地点的成本分布
- **人工成本详情** - 员工工作时长、完成件数、成本金额

**报表导出**:
- 支持导出Excel格式的成本分析报告
- 包含详细的成本明细和汇总数据

## ⚙️ 自动化配置

### 生产阶段自动化

系统支持生产阶段变更时的自动化触发，包括：

1. **库存自动转移** - 阶段变更时自动创建库存转移请求
2. **成本自动记录** - 自动记录阶段相关的成本
3. **质量检验触发** - 特定阶段自动创建质量检验任务
4. **状态自动更新** - 自动更新生产订单状态
5. **通知自动发送** - 向相关人员发送阶段变更通知

### 自动化规则示例

```json
{
  "ruleName": "生产完成自动转移",
  "triggerEvent": "STAGE_COMPLETED",
  "sourceStage": "IN_PRODUCTION",
  "targetStage": "QUALITY_CHECK",
  "transferType": "SEMI_PRODUCT_RETURN",
  "sourceWarehouse": "广西生产仓",
  "targetWarehouse": "广州质检仓",
  "isActive": true
}
```

## 📊 数据监控

### 实时统计

系统提供实时的数据统计和监控：

- **总转移次数** - 库存转移的总数量
- **待处理转移** - 需要处理的转移请求
- **自动化率** - 自动化规则的执行比例
- **物流成本** - 双地点物流的总成本

### 性能指标

- **API响应时间** ≤ 120ms
- **页面加载时间** ≤ 2秒
- **数据同步延迟** ≤ 5秒
- **自动化执行成功率** ≥ 95%

## 🔧 故障排除

### 常见问题

1. **库存转移失败**
   - 检查源仓库库存是否充足
   - 验证目标仓库是否可用
   - 确认转移权限设置

2. **自动化规则不执行**
   - 检查规则是否启用
   - 验证触发条件是否满足
   - 查看系统日志获取详细错误信息

3. **成本计算错误**
   - 验证计件单价设置
   - 检查质量等级配置
   - 确认员工工作记录准确性

### 技术支持

如遇到技术问题，请联系系统管理员或查看：
- 系统日志：设置 → 日志管理
- 诊断工具：设置 → 系统诊断
- 性能监控：设置 → 性能监控

## 📈 最佳实践

1. **定期检查自动化规则** - 确保规则配置符合实际业务需求
2. **及时审核计件工资** - 避免工资记录积压
3. **监控物流成本** - 优化运输路线和方式
4. **定期备份数据** - 确保数据安全
5. **培训操作人员** - 提高系统使用效率

## 🎯 系统优势

1. **智能自动化** - 减少人工操作，提高效率
2. **精确成本核算** - 实时成本跟踪和分析
3. **双地点协同** - 无缝的跨地点业务流程
4. **实时数据同步** - 确保数据一致性
5. **灵活配置** - 支持业务规则的灵活调整

---

*本指南涵盖了聆花珐琅双地点生产流程库存自动化管理和成本核算系统的主要功能和操作方法。如需更详细的操作说明，请参考系统内置帮助或联系技术支持。*
