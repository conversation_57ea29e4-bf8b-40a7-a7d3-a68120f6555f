# 聆花珐琅馆ERP系统模块规范

**文档版本**: v1.0  
**创建日期**: 2025-06-28  
**最后更新**: 2025-06-28  
**文档状态**: 正式版  
**维护人员**: 开发团队  

## 文档概述

本文档详细描述了聆花珐琅馆ERP系统的代码模块结构、API接口、数据模型、组件使用说明和开发指南，为开发人员提供技术实现参考。

## 目录

1. [代码模块结构](#1-代码模块结构)
2. [API接口文档](#2-api接口文档)
3. [数据模型说明](#3-数据模型说明)
4. [前端组件库](#4-前端组件库)
5. [业务逻辑层](#5-业务逻辑层)
6. [工具函数库](#6-工具函数库)
7. [中间件系统](#7-中间件系统)
8. [配置管理](#8-配置管理)
9. [测试规范](#9-测试规范)
10. [开发指南](#10-开发指南)

---

## 1. 代码模块结构

### 1.1 项目目录结构

```
src/
├── app/                    # Next.js App Router
│   ├── (main)/            # 主应用页面
│   │   ├── products/      # 产品管理页面
│   │   ├── orders/        # 订单管理页面
│   │   ├── inventory/     # 库存管理页面
│   │   ├── finance/       # 财务管理页面
│   │   └── reports/       # 报表分析页面
│   ├── (mobile)/          # 移动端页面
│   ├── api/               # API路由
│   │   ├── products/      # 产品相关API
│   │   ├── orders/        # 订单相关API
│   │   ├── inventory/     # 库存相关API
│   │   ├── finance/       # 财务相关API
│   │   └── auth/          # 认证相关API
│   └── globals.css        # 全局样式
├── components/            # React组件库
│   ├── ui/               # 基础UI组件
│   ├── forms/            # 表单组件
│   ├── charts/           # 图表组件
│   └── layout/           # 布局组件
├── lib/                  # 核心业务逻辑
│   ├── actions/          # 服务端操作
│   ├── services/         # 业务服务
│   ├── utils/            # 工具函数
│   ├── hooks/            # 自定义Hook
│   └── types/            # TypeScript类型定义
├── prisma/               # 数据库相关
│   ├── schema.prisma     # 数据模型定义
│   └── migrations/       # 数据库迁移
└── public/               # 静态资源
    ├── images/           # 图片资源
    └── icons/            # 图标资源
```### 1.2 模块依赖关系

```
页面组件 (app/*)
    ↓
业务组件 (components/*)
    ↓
业务逻辑 (lib/actions/*)
    ↓
数据服务 (lib/services/*)
    ↓
数据模型 (prisma/*)
```

---

## 2. API接口文档

### 2.1 产品管理API

**获取产品列表**
```
GET /api/products
```

**请求参数**:
```typescript
interface ProductListParams {
  page?: number          // 页码，默认1
  limit?: number         // 每页数量，默认20
  category?: string      // 产品分类
  status?: string        // 产品状态
  search?: string        // 搜索关键词
}
```

**响应格式**:
```typescript
interface ProductListResponse {
  success: boolean
  data: {
    products: Product[]
    total: number
    page: number
    limit: number
  }
  message: string
}
```

**创建产品**
```
POST /api/products
```

**请求体**:
```typescript
interface CreateProductRequest {
  name: string           // 产品名称
  price: number          // 产品价格
  categoryId: number     // 分类ID
  description?: string   // 产品描述
  sku?: string          // 产品SKU
  material?: string     // 材料
  unit?: string         // 单位
  imageUrl?: string     // 图片URL
}
```