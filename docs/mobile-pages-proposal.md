# 聆花掐丝珐琅馆ERP系统 - 移动端页面规划

本文档提供了聆花掐丝珐琅馆ERP系统移动端页面的详细规划方案，包括页面结构、导航设计和功能优先级。

## 移动端总体架构

移动端采用PWA（Progressive Web App）技术，确保离线可用性和与桌面版数据同步。移动端页面将专注于以下核心功能：

1. 产品和库存查询
2. 销售记录和POS操作
3. 简单的数据录入
4. 通知和提醒
5. 个人工作相关功能

## 路由结构

所有移动端页面将放置在 `app/(mobile)` 路由组下，使用 `/m` 前缀区分移动端页面。

### 核心页面

| 路径 | 功能描述 | 优先级 |
|------|----------|--------|
| `/m` | 移动端首页/仪表盘 | 高 |
| `/m/login` | 移动端登录 | 高 |
| `/m/profile` | 个人资料 | 中 |
| `/m/notifications` | 通知中心 | 中 |

### 产品与库存

| 路径 | 功能描述 | 优先级 |
|------|----------|--------|
| `/m/products` | 产品列表 | 高 |
| `/m/products/[id]` | 产品详情 | 高 |
| `/m/products/scan` | 扫码查询产品 | 中 |
| `/m/inventory` | 库存概览 | 高 |
| `/m/inventory/check` | 库存盘点 | 高 |
| `/m/inventory/transfer` | 库存调拨 | 中 |

### 销售功能

| 路径 | 功能描述 | 优先级 |
|------|----------|--------|
| `/m/pos` | 移动POS销售 | 高 |
| `/m/pos/history` | 销售历史 | 中 |
| `/m/sales` | 销售订单列表 | 中 |
| `/m/sales/[id]` | 销售订单详情 | 中 |

### 数据录入

| 路径 | 功能描述 | 优先级 |
|------|----------|--------|
| `/m/daily-log` | 日常数据录入 | 高 |
| `/m/coffee-log` | 咖啡店数据录入 | 高 |
| `/m/workshop-log` | 团建活动记录 | 中 |

### 员工相关

| 路径 | 功能描述 | 优先级 |
|------|----------|--------|
| `/m/schedule` | 个人排班查看 | 高 |
| `/m/attendance` | 考勤打卡 | 高 |
| `/m/salary` | 个人薪资查询 | 中 |

### 审批与工作流

| 路径 | 功能描述 | 优先级 |
|------|----------|--------|
| `/m/approvals` | 待我审批 | 中 |
| `/m/workflows/my` | 我的工作流 | 低 |

### 设置与帮助

| 路径 | 功能描述 | 优先级 |
|------|----------|--------|
| `/m/settings` | 移动端设置 | 低 |
| `/m/help` | 帮助中心 | 低 |

## 移动端导航设计

### 底部导航栏

移动端采用底部导航栏设计，包含5个核心功能入口：

1. **首页** - 仪表盘和快捷功能
2. **产品** - 产品查询和库存管理
3. **销售** - POS销售和订单管理
4. **数据** - 各类数据录入
5. **我的** - 个人相关功能和设置

### 手势操作

为提升移动端用户体验，设计以下手势操作：

- 左右滑动切换相关页面（如产品列表中的产品详情）
- 下拉刷新数据
- 上拉加载更多
- 长按项目显示快捷操作菜单

### 快捷功能

在首页提供以下快捷功能：

- 扫码查询产品
- 快速POS销售
- 库存盘点入口
- 数据录入入口
- 待办事项和通知

## 离线功能设计

移动端需要支持以下离线功能：

1. **产品和库存查询**：
   - 缓存产品基本信息和图片
   - 缓存最近查询的库存数据

2. **销售记录**：
   - 离线记录销售数据
   - 网络恢复后自动同步

3. **数据录入**：
   - 离线保存录入的数据
   - 网络恢复后自动上传

4. **文档和帮助**：
   - 完全缓存帮助文档
   - 操作指南离线可用

## 数据同步策略

移动端与桌面版的数据同步策略：

1. **实时同步**：
   - 销售数据
   - 库存变动
   - 重要通知

2. **定期同步**：
   - 产品信息更新
   - 非关键配置数据
   - 报表数据

3. **手动同步**：
   - 大量历史数据
   - 图片资源
   - 系统设置

## 移动端特有功能

以下是移动端特有的功能，桌面版不提供或提供不同实现：

1. **扫码功能**：
   - 产品条码扫描
   - 二维码支付集成
   - 库存盘点扫码

2. **地理位置功能**：
   - 员工签到定位
   - 外勤工作记录
   - 渠道合作伙伴地图

3. **相机集成**：
   - 产品拍照上传
   - 文档扫描
   - 问题记录拍照

4. **推送通知**：
   - 库存预警推送
   - 审批提醒
   - 重要事件通知

## 实施路线图

### 第一阶段（1-2个月）

- 移动端基础架构搭建
- 核心页面实现：首页、登录、产品列表、产品详情
- 基本导航结构实现

### 第二阶段（2-3个月）

- POS销售功能实现
- 库存查询和盘点功能
- 数据录入功能
- 离线功能基础实现

### 第三阶段（3-4个月）

- 扫码功能集成
- 推送通知实现
- 完整的离线功能支持
- 性能优化和用户体验提升

## 技术实现建议

1. **前端技术**：
   - Next.js 13+ App Router
   - React Server Components
   - Tailwind CSS（移动优先设计）
   - SWR/React Query（数据获取和缓存）

2. **PWA功能**：
   - Service Worker（离线缓存）
   - Web App Manifest
   - IndexedDB（本地数据存储）
   - Background Sync API（数据同步）

3. **移动端集成**：
   - 相机API
   - 地理位置API
   - 设备传感器API
   - Web Push API

4. **性能优化**：
   - 代码分割
   - 图片优化
   - 延迟加载
   - 预取数据

## 设计原则

1. **移动优先**：
   - 触摸友好的界面元素
   - 适合单手操作的布局
   - 清晰的视觉层次

2. **简洁高效**：
   - 减少页面跳转
   - 关键信息优先展示
   - 减少输入操作

3. **一致性**：
   - 与桌面版保持视觉一致性
   - 功能命名和位置保持一致
   - 数据展示格式统一

4. **性能优先**：
   - 快速加载和响应
   - 平滑的动画和过渡
   - 低网络环境下的可用性
