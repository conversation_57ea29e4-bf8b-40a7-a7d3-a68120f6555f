generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                      String                   @id @default(cuid())
  name                    String?
  email                   String?                  @unique
  emailVerified           DateTime?
  image                   String?
  password                String?
  role                    String                   @default("user")
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  bio                     String?
  employeeId              Int?                     @unique
  lastLogin               DateTime?
  phone                   String?
  resetToken              String?
  resetTokenExpiry        DateTime?
  roles                   Int[]                    @default([])
  passwordLastChanged     DateTime?
  failedLoginAttempts     Int                      @default(0)
  lockedUntil             DateTime?
  dashboardLayouts        DashboardLayout[]
  receivedMessages        Message[]                @relation("ReceivedMessages")
  sentMessages            Message[]                @relation("SentMessages")
  messageRecipients       MessageRecipient[]
  notifications           Notification[]
  purchaseOrderApprovals  PurchaseOrderApproval[]
  purchaseOrderImports    PurchaseOrderImport[]
  purchaseOrderReceivings PurchaseOrderReceiving[]
  reportConfigs           ReportConfig[]
  todos                   Todo[]
  employee                Employee?                @relation(fields: [employeeId], references: [id])
  favorites               UserFavorite[]
  loginHistory            UserLoginHistory[]
  preferences             UserPreference[]
  userRoles               UserRole[]
  userSettings            UserSettings?
}

model Employee {
  id                  Int                  @id @default(autoincrement())
  name                String
  position            String
  phone               String?
  email               String?
  dailySalary         Float
  status              String               @default("active")
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  coffeeShopPurchases CoffeeShopPurchase[] @relation("CoffeeShopPurchaseEmployee")
  coffeeShopSales     CoffeeShopSale[]     @relation("CoffeeShopSaleEmployee")
  coffeeShopShifts    CoffeeShopShift[]
  gallerySales        GallerySale[]
  orders              Order[]
  pieceWorks          PieceWork[]
  posSales            PosSale[]
  productionOrders    ProductionOrder[]
  purchaseOrders      PurchaseOrder[]
  qualityInspections  QualityRecord[]
  salaryAdjustments   SalaryAdjustment[]
  salaryRecords       SalaryRecord[]
  schedules           Schedule[]
  user                User?
  assistedWorkshops   Workshop[]           @relation("AssistantWorkshops")
  managedWorkshops    Workshop[]           @relation("ManagerWorkshops")
  workshops           Workshop[]           @relation("TeacherWorkshops")
  workshopTeamMember  WorkshopTeamMember?

  @@index([name])
  @@index([position])
  @@index([status])
  @@index([email])
  @@index([createdAt])
}

model Product {
  id                    Int                     @id @default(autoincrement())
  name                  String
  price                 Float
  commissionRate        Float
  type                  String                  @default("product")
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  description           String?
  imageUrl              String?
  barcode               String?
  category              String?
  cost                  Float?
  sku                   String?
  categoryId            Int?
  details               String?
  dimensions            String?
  imageUrls             String[]                @default([])
  inventory             Int?
  material              String?
  unit                  String?
  channelInventory      ChannelInventory[]
  channelPrices         ChannelPrice[]
  channelSaleItems      ChannelSaleItem[]
  financeRecords        FinanceRecord[]
  inventoryItems        InventoryItem[]
  inventoryTransactions InventoryTransaction[]
  orderItems            OrderItem[]
  posSaleItems          PosSaleItem[]
  productCategory       ProductCategory?        @relation(fields: [categoryId], references: [id])
  productTags           ProductTagsOnProducts[]
  productionOrderItems  ProductionOrderItem[]
  purchaseOrderItems    PurchaseOrderItem[]
  qualityRecords        QualityRecord[]
  salesItems            SalesItem[]
  workshops             Workshop[]
  workshopActivities    WorkshopActivity[]
  workshopServiceItems  WorkshopServiceItem[]

  @@index([name])
  @@index([sku])
  @@index([price])
  @@index([categoryId])
  @@index([material])
  @@index([type])
  @@index([name, categoryId])
  @@index([price, categoryId])
  @@index([createdAt])
  @@index([updatedAt])
}

model PieceWorkItem {
  id               Int               @id @default(autoincrement())
  name             String
  price            Float
  type             String
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  pieceWorkDetails PieceWorkDetail[]
}

model Schedule {
  id         Int      @id @default(autoincrement())
  employeeId Int
  date       DateTime
  startTime  String
  endTime    String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  note       String?
  employee   Employee @relation(fields: [employeeId], references: [id])
}

model ScheduleTemplate {
  id          Int      @id @default(autoincrement())
  name        String
  startTime   String
  endTime     String
  weekdays    Int[]
  employeeIds Int[]
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model GallerySale {
  id          Int            @id @default(autoincrement())
  employeeId  Int
  date        DateTime
  totalAmount Float
  notes       String?
  imageUrl    String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  employee    Employee       @relation(fields: [employeeId], references: [id])
  salesItems  SalesItem[]
  files       UploadedFile[]
}

model Workshop {
  id            Int                   @id @default(autoincrement())
  date          DateTime
  productId     Int?
  teacherId     Int
  assistantId   Int?
  role          String
  locationType  String
  location      String
  participants  Int
  duration      Float
  notes         String?
  createdAt     DateTime              @default(now())
  updatedAt     DateTime              @updatedAt
  activityId    Int?
  activityType  String?
  baseType      String?
  channelId     Int?
  customerId    Int?
  depositAmount Float                 @default(0)
  managerId     Int?
  paymentMethod String?
  paymentStatus String?               @default("unpaid")
  status        String?               @default("completed")
  totalAmount   Float                 @default(0)
  activity      WorkshopActivity?     @relation(fields: [activityId], references: [id])
  assistant     Employee?             @relation("AssistantWorkshops", fields: [assistantId], references: [id])
  customer      Customer?             @relation(fields: [customerId], references: [id])
  manager       Employee?             @relation("ManagerWorkshops", fields: [managerId], references: [id])
  product       Product?              @relation(fields: [productId], references: [id])
  teacher       Employee              @relation("TeacherWorkshops", fields: [teacherId], references: [id])
  serviceItems  WorkshopServiceItem[]
}

model WorkshopActivity {
  id              Int             @id @default(autoincrement())
  name            String
  description     String?
  productId       Int
  duration        Float
  minParticipants Int
  maxParticipants Int
  price           Float
  materialFee     Float           @default(0)
  teacherFee      Float           @default(0)
  assistantFee    Float           @default(0)
  isActive        Boolean         @default(true)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  workshops       Workshop[]
  product         Product         @relation(fields: [productId], references: [id])
  prices          WorkshopPrice[]
}

model PieceWork {
  id          Int               @id @default(autoincrement())
  employeeId  Int
  date        DateTime
  workType    String
  totalAmount Float
  notes       String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  employee    Employee          @relation(fields: [employeeId], references: [id])
  details     PieceWorkDetail[]
}

model CoffeeShopSale {
  id             Int               @id @default(autoincrement())
  date           DateTime
  totalSales     Float
  notes          String?
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  alipayAmount   Float             @default(0)
  cardAmount     Float             @default(0)
  cashAmount     Float             @default(0)
  customerCount  Int               @default(0)
  otherAmount    Float             @default(0)
  wechatAmount   Float             @default(0)
  employeeId     Int?
  paymentMethods String?
  items          CoffeeShopItem[]
  employee       Employee?         @relation("CoffeeShopSaleEmployee", fields: [employeeId], references: [id])
  shifts         CoffeeShopShift[]
}

model CoffeeShopPurchase {
  id            Int      @id @default(autoincrement())
  date          DateTime
  supplier      String
  employeeId    Int
  items         String
  totalAmount   Float
  paymentMethod String?
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  employee      Employee @relation("CoffeeShopPurchaseEmployee", fields: [employeeId], references: [id])
}

model UploadedFile {
  id            Int          @id @default(autoincrement())
  filename      String
  originalName  String?
  path          String
  mimetype      String
  size          Int
  gallerySaleId Int?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  gallerySale   GallerySale? @relation(fields: [gallerySaleId], references: [id])
}

model SalesItem {
  id            Int         @id @default(autoincrement())
  gallerySaleId Int
  productId     Int?
  quantity      Int
  price         Float
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  gallerySale   GallerySale @relation(fields: [gallerySaleId], references: [id])
  product       Product?    @relation(fields: [productId], references: [id])
}

model PieceWorkDetail {
  id              Int           @id @default(autoincrement())
  pieceWorkId     Int
  pieceWorkItemId Int
  quantity        Int
  price           Float
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  pieceWork       PieceWork     @relation(fields: [pieceWorkId], references: [id])
  pieceWorkItem   PieceWorkItem @relation(fields: [pieceWorkItemId], references: [id])
}

model CoffeeShopShift {
  id               Int            @id @default(autoincrement())
  coffeeShopSaleId Int
  employeeId       Int
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  coffeeShopSale   CoffeeShopSale @relation(fields: [coffeeShopSaleId], references: [id])
  employee         Employee       @relation(fields: [employeeId], references: [id])
}

model CoffeeShopItem {
  id               Int            @id @default(autoincrement())
  coffeeShopSaleId Int
  name             String
  category         String
  quantity         Int
  unitPrice        Float
  totalPrice       Float
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  coffeeShopSale   CoffeeShopSale @relation(fields: [coffeeShopSaleId], references: [id])
}

model SystemSetting {
  id                          Int      @id @default(autoincrement())
  companyName                 String
  coffeeSalesCommissionRate   Float
  gallerySalesCommissionRate  Float
  teacherWorkshopFee          Float
  assistantWorkshopFee        Float
  enableImageUpload           Boolean  @default(true)
  enableNotifications         Boolean  @default(true)
  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt
  basicWorkingDays            Int      @default(22)
  basicWorkingHours           Float    @default(8)
  holidayOvertimeRate         Float    @default(3)
  overtimeRate                Float    @default(1.5)
  socialInsuranceRate         Float    @default(0)
  taxRate                     Float    @default(0)
  weekendOvertimeRate         Float    @default(2)
  assistantWorkshopFeeInside  Float    @default(110)
  assistantWorkshopFeeOutside Float    @default(130)
  teacherWorkshopFeeInside    Float    @default(180)
  teacherWorkshopFeeOutside   Float    @default(200)
}

model CompanyProfile {
  id                  Int       @id @default(autoincrement())
  companyName         String
  companyNameEn       String?
  logoUrl             String?
  address             String
  city                String?
  province            String?
  postalCode          String?
  country             String    @default("中国")
  phone               String
  fax                 String?
  email               String
  website             String?
  taxNumber           String?
  businessLicense     String?
  legalRepresentative String?
  registeredCapital   String?
  businessScope       String?
  description         String?
  foundedDate         DateTime?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  @@index([companyName])
}

model SystemParameter {
  id          Int      @id @default(autoincrement())
  key         String   @unique
  value       String
  description String?
  group       String   @default("general")
  type        String   @default("string")
  options     String?
  isSystem    Boolean  @default(false)
  isReadonly  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([group])
  @@index([key])
}

model Warehouse {
  id                      Int                      @id @default(autoincrement())
  name                    String
  type                    String                   @default("physical")
  location                String?
  description             String?
  isActive                Boolean                  @default(true)
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  code                    String?
  isDefault               Boolean                  @default(false)
  productionBaseId        Int?
  inventoryItems          InventoryItem[]          @relation("WarehouseToInventoryItem")
  sourceTransactions      InventoryTransaction[]   @relation("SourceWarehouse")
  targetTransactions      InventoryTransaction[]   @relation("TargetWarehouse")
  purchaseOrderReceivings PurchaseOrderReceiving[]
}

model InventoryItem {
  id          Int       @id @default(autoincrement())
  warehouseId Int
  productId   Int
  quantity    Int
  minQuantity Int?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  notes       String?
  product     Product   @relation(fields: [productId], references: [id])
  warehouse   Warehouse @relation("WarehouseToInventoryItem", fields: [warehouseId], references: [id])

  @@unique([warehouseId, productId])
  @@index([productId])
  @@index([warehouseId])
  @@index([quantity])
}

model InventoryTransaction {
  id                   Int                    @id @default(autoincrement())
  type                 String
  sourceWarehouseId    Int?
  targetWarehouseId    Int?
  productId            Int
  quantity             Int
  notes                String?
  referenceId          Int?
  referenceType        String?
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  attachmentUrl        String?
  relatedTransactionId Int?
  productionOrderId    Int?
  qualityStatus        String?
  product              Product                @relation(fields: [productId], references: [id])
  relatedTransaction   InventoryTransaction?  @relation("RelatedTransactions", fields: [relatedTransactionId], references: [id])
  relatedTransactions  InventoryTransaction[] @relation("RelatedTransactions")
  sourceWarehouse      Warehouse?             @relation("SourceWarehouse", fields: [sourceWarehouseId], references: [id])
  targetWarehouse      Warehouse?             @relation("TargetWarehouse", fields: [targetWarehouseId], references: [id])
}

model Customer {
  id        Int        @id @default(autoincrement())
  name      String
  phone     String?
  email     String?
  address   String?
  type      String     @default("individual")
  notes     String?
  isActive  Boolean    @default(true)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  orders    Order[]
  workshops Workshop[]
}

model Order {
  id                   Int         @id @default(autoincrement())
  orderNumber          String      @unique
  customerId           Int
  employeeId           Int
  orderDate            DateTime
  status               String      @default("pending")
  totalAmount          Float
  paidAmount           Float       @default(0)
  paymentStatus        String      @default("unpaid")
  paymentMethod        String?
  notes                String?
  createdAt            DateTime    @default(now())
  updatedAt            DateTime    @updatedAt
  customDesign         String?
  customRequirements   String?
  designApproved       Boolean?
  designImageUrl       String?
  designerNotes        String?
  expectedDeliveryDate DateTime?
  isCustom             Boolean     @default(false)
  customer             Customer    @relation(fields: [customerId], references: [id])
  employee             Employee    @relation(fields: [employeeId], references: [id])
  items                OrderItem[]

  @@index([customerId])
  @@index([employeeId])
  @@index([orderDate])
  @@index([status])
  @@index([paymentStatus])
  @@index([orderDate, status])
  @@index([customerId, orderDate])
}

model OrderItem {
  id        Int      @id @default(autoincrement())
  orderId   Int
  productId Int?
  quantity  Int
  price     Float
  discount  Float    @default(0)
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  order     Order    @relation(fields: [orderId], references: [id])
  product   Product? @relation(fields: [productId], references: [id])
}

model Channel {
  id               Int                   @id @default(autoincrement())
  name             String
  code             String                @unique
  description      String?
  contactName      String?
  contactPhone     String?
  contactEmail     String?
  address          String?
  isActive         Boolean               @default(true)
  createdAt        DateTime              @default(now())
  updatedAt        DateTime              @updatedAt
  bankAccount      String?
  bankName         String?
  cooperationStart DateTime?
  settlementCycle  Int                   @default(1)
  status           String                @default("active")
  deposits         ChannelDeposit[]
  distributions    ChannelDistribution[]
  inventory        ChannelInventory[]
  prices           ChannelPrice[]
  sales            ChannelSale[]
  settlements      ChannelSettlement[]
  workshopPrices   WorkshopPrice[]
}

model ChannelPrice {
  id        Int      @id @default(autoincrement())
  channelId Int
  productId Int
  price     Float
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  channel   Channel  @relation(fields: [channelId], references: [id])
  product   Product  @relation(fields: [productId], references: [id])

  @@unique([channelId, productId])
}

model SalaryRecord {
  id                    Int                @id @default(autoincrement())
  employeeId            Int
  year                  Int
  month                 Int
  baseSalary            Float
  scheduleSalary        Float
  salesCommission       Float
  pieceWorkIncome       Float
  workshopIncome        Float
  coffeeShiftCommission Float
  overtimePay           Float
  bonus                 Float
  deductions            Float
  socialInsurance       Float
  tax                   Float
  totalIncome           Float
  netIncome             Float
  status                String             @default("draft")
  paymentDate           DateTime?
  notes                 String?
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  adjustments           SalaryAdjustment[]
  employee              Employee           @relation(fields: [employeeId], references: [id])

  @@unique([employeeId, year, month])
}

model SalaryAdjustment {
  id             Int           @id @default(autoincrement())
  employeeId     Int
  adjustmentDate DateTime
  oldSalary      Float
  newSalary      Float
  reason         String
  approvedBy     String?
  notes          String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  salaryRecordId Int?
  employee       Employee      @relation(fields: [employeeId], references: [id])
  salaryRecord   SalaryRecord? @relation(fields: [salaryRecordId], references: [id])
}

model Supplier {
  id             Int             @id @default(autoincrement())
  name           String
  contactPerson  String?
  phone          String?
  email          String?
  address        String?
  description    String?
  isActive       Boolean         @default(true)
  supplierType   String          @default("material")
  balance        Float           @default(0)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  financeRecords FinanceRecord[]
  purchaseOrders PurchaseOrder[]
}

model ProductionBase {
  id               Int               @id @default(autoincrement())
  name             String
  code             String            @unique
  location         String
  contactName      String?
  contactPhone     String?
  contactEmail     String?
  address          String?
  specialties      String[]          @default([])
  capacity         Int?
  leadTime         Int?
  qualityRating    Float?
  isActive         Boolean           @default(true)
  notes            String?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  productionOrders ProductionOrder[]
  qualityRecords   QualityRecord[]
}

model ProductionOrder {
  id                Int                   @id @default(autoincrement())
  orderNumber       String                @unique
  productionBaseId  Int
  employeeId        Int
  sourceOrderId     Int?
  orderDate         DateTime
  expectedStartDate DateTime?
  expectedEndDate   DateTime?
  actualStartDate   DateTime?
  actualEndDate     DateTime?
  status            String                @default("pending")
  priority          String                @default("normal")
  totalAmount       Float
  paidAmount        Float                 @default(0)
  paymentStatus     String                @default("unpaid")
  paymentMethod     String?
  shippingMethod    String?
  trackingNumber    String?
  notes             String?
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  employee          Employee              @relation(fields: [employeeId], references: [id])
  productionBase    ProductionBase        @relation(fields: [productionBaseId], references: [id])
  items             ProductionOrderItem[]
  qualityRecords    QualityRecord[]
  shippingRecords   ShippingRecord[]
}

model ProductionOrderItem {
  id                Int             @id @default(autoincrement())
  productionOrderId Int
  productId         Int?
  quantity          Int
  specifications    String?
  completedQuantity Int             @default(0)
  qualityStatus     String          @default("pending")
  notes             String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  product           Product?        @relation(fields: [productId], references: [id])
  productionOrder   ProductionOrder @relation(fields: [productionOrderId], references: [id])
}

model QualityRecord {
  id                Int              @id @default(autoincrement())
  productionOrderId Int?
  productionBaseId  Int
  productId         Int
  inspectorId       Int
  inspectionDate    DateTime
  qualityGrade      String
  qualityScore      Float?
  defectDescription String?
  actionRequired    String?
  status            String           @default("pending")
  images            String[]         @default([])
  notes             String?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  inspector         Employee         @relation(fields: [inspectorId], references: [id])
  product           Product          @relation(fields: [productId], references: [id])
  productionBase    ProductionBase   @relation(fields: [productionBaseId], references: [id])
  productionOrder   ProductionOrder? @relation(fields: [productionOrderId], references: [id])
}

model ShippingRecord {
  id                Int             @id @default(autoincrement())
  productionOrderId Int
  shippingType      String
  shippingDate      DateTime
  expectedDate      DateTime?
  actualDate        DateTime?
  carrier           String?
  trackingNumber    String?
  shippingCost      Float?
  status            String          @default("pending")
  notes             String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  productionOrder   ProductionOrder @relation(fields: [productionOrderId], references: [id])
}

model PurchaseOrder {
  id             Int                      @id @default(autoincrement())
  orderNumber    String                   @unique
  supplierId     Int
  employeeId     Int
  orderDate      DateTime
  expectedDate   DateTime?
  status         String                   @default("pending")
  approvalStatus String                   @default("draft")
  currentStep    Int                      @default(0)
  totalAmount    Float
  paidAmount     Float                    @default(0)
  paymentStatus  String                   @default("unpaid")
  paymentMethod  String?
  notes          String?
  importBatchId  String?
  createdAt      DateTime                 @default(now())
  updatedAt      DateTime                 @updatedAt
  employee       Employee                 @relation(fields: [employeeId], references: [id])
  importRecord   PurchaseOrderImport?     @relation(fields: [importBatchId], references: [batchId])
  supplier       Supplier                 @relation(fields: [supplierId], references: [id])
  approvals      PurchaseOrderApproval[]
  items          PurchaseOrderItem[]
  receivings     PurchaseOrderReceiving[]
}

model PurchaseOrderItem {
  id               Int                          @id @default(autoincrement())
  purchaseOrderId  Int
  productId        Int?
  productName      String?
  quantity         Int
  price            Float
  receivedQuantity Int                          @default(0)
  notes            String?
  createdAt        DateTime                     @default(now())
  updatedAt        DateTime                     @updatedAt
  product          Product?                     @relation(fields: [productId], references: [id])
  purchaseOrder    PurchaseOrder                @relation(fields: [purchaseOrderId], references: [id])
  receivingItems   PurchaseOrderReceivingItem[]
}

model PurchaseOrderApproval {
  id              String        @id @default(cuid())
  purchaseOrderId Int
  workflowStepId  Int?
  approverId      String
  approverName    String
  status          String        @default("pending")
  comments        String?
  approvedAt      DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  approver        User          @relation(fields: [approverId], references: [id])
  purchaseOrder   PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  workflowStep    WorkflowStep? @relation(fields: [workflowStepId], references: [id])
}

model PurchaseOrderImport {
  id             Int             @id @default(autoincrement())
  batchId        String          @unique @default(cuid())
  fileName       String
  filePath       String?
  importedBy     String
  totalRows      Int
  successRows    Int
  failedRows     Int
  status         String          @default("processing")
  errorLog       String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  purchaseOrders PurchaseOrder[]
  importer       User            @relation(fields: [importedBy], references: [id])
}

model PurchaseOrderReceiving {
  id              Int                          @id @default(autoincrement())
  purchaseOrderId Int
  receivingNumber String                       @unique
  warehouseId     Int
  receivedDate    DateTime
  receivedBy      String
  receiverName    String
  totalReceived   Int
  totalExpected   Int
  status          String                       @default("partial")
  qualityNotes    String?
  notes           String?
  createdAt       DateTime                     @default(now())
  updatedAt       DateTime                     @updatedAt
  purchaseOrder   PurchaseOrder                @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  receiver        User                         @relation(fields: [receivedBy], references: [id])
  warehouse       Warehouse                    @relation(fields: [warehouseId], references: [id])
  items           PurchaseOrderReceivingItem[]
}

model PurchaseOrderReceivingItem {
  id                  Int                    @id @default(autoincrement())
  receivingId         Int
  purchaseOrderItemId Int
  expectedQuantity    Int
  receivedQuantity    Int
  qualityGrade        String                 @default("A")
  qualityNotes        String?
  unitCost            Float?
  notes               String?
  createdAt           DateTime               @default(now())
  updatedAt           DateTime               @updatedAt
  purchaseOrderItem   PurchaseOrderItem      @relation(fields: [purchaseOrderItemId], references: [id], onDelete: Cascade)
  receiving           PurchaseOrderReceiving @relation(fields: [receivingId], references: [id], onDelete: Cascade)
}

model Role {
  id              Int              @id @default(autoincrement())
  name            String
  code            String           @unique
  description     String?
  isSystem        Boolean          @default(false)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  rolePermissions RolePermission[]
  userRoles       UserRole[]
}

model Permission {
  id              Int              @id @default(autoincrement())
  name            String
  code            String           @unique
  module          String
  description     String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  rolePermissions RolePermission[]
}

model UserRole {
  id        Int      @id @default(autoincrement())
  userId    String
  roleId    Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  role      Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
}

model RolePermission {
  id           Int        @id @default(autoincrement())
  roleId       Int
  permissionId Int
  createdAt    DateTime   @default(now())
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
}

model UserSettings {
  id                  Int      @id @default(autoincrement())
  userId              String   @unique
  theme               String   @default("light")
  language            String   @default("zh-CN")
  enableNotifications Boolean  @default(true)
  enableTwoFactorAuth Boolean  @default(false)
  twoFactorAuthSecret String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserPreference {
  id        String   @id @default(cuid())
  userId    String
  category  String
  key       String
  value     Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, category, key])
  @@index([userId, category])
}

model DashboardLayout {
  id        String   @id @default(cuid())
  userId    String
  name      String
  isDefault Boolean  @default(false)
  layout    Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model UserFavorite {
  id          String    @id @default(cuid())
  userId      String
  type        String
  title       String
  url         String?
  icon        String?
  category    String?
  description String?
  config      Json?
  sortOrder   Int       @default(0)
  isShared    Boolean   @default(false)
  sharedWith  String[]  @default([])
  accessCount Int       @default(0)
  lastAccess  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, type])
  @@index([userId, category])
}

model ReportConfig {
  id          String   @id @default(cuid())
  userId      String
  reportType  String
  name        String
  description String?
  config      Json
  isDefault   Boolean  @default(false)
  isShared    Boolean  @default(false)
  sharedWith  String[] @default([])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, reportType])
}

model UserLoginHistory {
  id        Int      @id @default(autoincrement())
  userId    String
  ipAddress String
  userAgent String
  loginTime DateTime
  status    String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model SystemLog {
  id        Int      @id @default(autoincrement())
  module    String
  level     String
  message   String
  details   Json?
  userId    String?
  action    String?
  ipAddress String?
  userAgent String?
  sessionId String?
  requestId String?
  timestamp DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@index([module])
  @@index([level])
  @@index([userId])
  @@index([timestamp])
  @@index([createdAt])
}

model Notification {
  id        String    @id @default(cuid())
  userId    String
  title     String
  message   String
  type      String
  priority  String
  read      Boolean   @default(false)
  link      String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  expiresAt DateTime?
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Todo {
  id          String    @id @default(cuid())
  userId      String
  title       String
  description String?
  type        String
  priority    String
  status      String    @default("pending")
  completed   Boolean   @default(false)
  dueDate     DateTime?
  link        String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  completedAt DateTime?
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Message {
  id          String             @id @default(cuid())
  senderId    String
  recipientId String?
  subject     String
  content     String
  type        String             @default("chat")
  priority    String             @default("normal")
  read        Boolean            @default(false)
  readAt      DateTime?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  recipient   User?              @relation("ReceivedMessages", fields: [recipientId], references: [id], onDelete: Cascade)
  sender      User               @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  recipients  MessageRecipient[]
}

model MessageRecipient {
  id        String    @id @default(cuid())
  messageId String
  userId    String
  read      Boolean   @default(false)
  readAt    DateTime?
  createdAt DateTime  @default(now())
  message   Message   @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([messageId, userId])
}

model AuditLog {
  id         Int      @id @default(autoincrement())
  userId     String?
  action     String
  entityType String
  entityId   String
  oldValues  String?
  newValues  String?
  ipAddress  String?
  userAgent  String?
  timestamp  DateTime @default(now())
  details    String?
}

model DataDictionary {
  id          Int                  @id @default(autoincrement())
  code        String               @unique
  name        String
  description String?
  isSystem    Boolean              @default(false)
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  items       DataDictionaryItem[]
}

model DataDictionaryItem {
  id           Int            @id @default(autoincrement())
  dictionaryId Int
  code         String
  value        String
  label        String
  sortOrder    Int            @default(0)
  isDefault    Boolean        @default(false)
  isActive     Boolean        @default(true)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  dictionary   DataDictionary @relation(fields: [dictionaryId], references: [id], onDelete: Cascade)

  @@unique([dictionaryId, code])
}

model Workflow {
  id          Int                @id @default(autoincrement())
  code        String             @unique
  name        String
  description String?
  entityType  String
  isActive    Boolean            @default(true)
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  instances   WorkflowInstance[]
  steps       WorkflowStep[]
}

model WorkflowStep {
  id                     Int                     @id @default(autoincrement())
  workflowId             Int
  name                   String
  description            String?
  stepNumber             Int
  approverType           String
  approverId             String?
  isRequired             Boolean                 @default(true)
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  purchaseOrderApprovals PurchaseOrderApproval[]
  approvals              WorkflowApproval[]
  workflow               Workflow                @relation(fields: [workflowId], references: [id], onDelete: Cascade)
}

model WorkflowInstance {
  id                String             @id @default(cuid())
  workflowId        Int
  entityId          String
  status            String             @default("pending")
  initiatedBy       String
  initiatedAt       DateTime           @default(now())
  completedAt       DateTime?
  currentStepNumber Int?
  notes             String?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  approvals         WorkflowApproval[]
  workflow          Workflow           @relation(fields: [workflowId], references: [id])
}

model WorkflowApproval {
  id                 String           @id @default(cuid())
  workflowInstanceId String
  workflowStepId     Int
  approverId         String
  status             String           @default("pending")
  comments           String?
  actionDate         DateTime?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  workflowInstance   WorkflowInstance @relation(fields: [workflowInstanceId], references: [id], onDelete: Cascade)
  workflowStep       WorkflowStep     @relation(fields: [workflowStepId], references: [id])
}

model ProductTag {
  id          Int                     @id @default(autoincrement())
  name        String                  @unique
  color       String?
  description String?
  createdAt   DateTime                @default(now())
  updatedAt   DateTime                @updatedAt
  products    ProductTagsOnProducts[]
}

model ProductTagsOnProducts {
  productId Int
  tagId     Int
  createdAt DateTime   @default(now())
  product   Product    @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag       ProductTag @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([productId, tagId])
  @@index([productId])
  @@index([tagId])
}

model ChannelInventory {
  id                   Int                   @id @default(autoincrement())
  channelId            Int
  productId            Int
  quantity             Int                   @default(0)
  minQuantity          Int?
  notes                String?
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  channelDistributions ChannelDistribution[]
  channel              Channel               @relation(fields: [channelId], references: [id])
  product              Product               @relation(fields: [productId], references: [id])
  channelSaleItems     ChannelSaleItem[]

  @@unique([channelId, productId])
}

model WorkshopTeamMember {
  id                 Int      @id @default(autoincrement())
  employeeId         Int      @unique
  role               String
  specialties        String[]
  rating             Float    @default(5.0)
  maxWorkshopsPerDay Int      @default(2)
  isActive           Boolean  @default(true)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  employee           Employee @relation(fields: [employeeId], references: [id])
}

model WorkshopServiceItem {
  id         Int      @id @default(autoincrement())
  workshopId Int
  productId  Int
  quantity   Int
  price      Float
  notes      String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  product    Product  @relation(fields: [productId], references: [id])
  workshop   Workshop @relation(fields: [workshopId], references: [id], onDelete: Cascade)
}

model WorkshopPrice {
  id              Int               @id @default(autoincrement())
  activityId      Int?
  channelId       Int
  basePrice       Float
  pricePerPerson  Float
  minParticipants Int
  maxParticipants Int
  materialFee     Float             @default(0)
  teacherFee      Float             @default(0)
  assistantFee    Float             @default(0)
  isActive        Boolean           @default(true)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  activity        WorkshopActivity? @relation(fields: [activityId], references: [id])
  channel         Channel           @relation(fields: [channelId], references: [id])

  @@unique([activityId, channelId])
}

model ProductCategory {
  id          Int               @id @default(autoincrement())
  name        String
  code        String?
  description String?
  imageUrl    String?
  isActive    Boolean           @default(true)
  sortOrder   Int               @default(0)
  parentId    Int?
  level       Int               @default(1)
  path        String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  products    Product[]
  parent      ProductCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    ProductCategory[] @relation("CategoryHierarchy")
}

model Artwork {
  id              Int                     @id @default(autoincrement())
  name            String
  price           Float
  commissionRate  Float
  type            String                  @default("artwork")
  createdAt       DateTime                @default(now())
  updatedAt       DateTime                @updatedAt
  description     String?
  imageUrl        String?
  barcode         String?
  category        String?
  cost            Float?
  sku             String?
  categoryId      Int?
  details         String?
  dimensions      String?
  imageUrls       String[]                @default([])
  inventory       Int?
  material        String?
  unit            String?
  artworkCategory ArtworkCategory?        @relation(fields: [categoryId], references: [id])
  artworkTags     ArtworkTagsOnArtworks[]
}

model ArtworkCategory {
  id          Int               @id @default(autoincrement())
  name        String
  code        String?
  description String?
  imageUrl    String?
  isActive    Boolean           @default(true)
  sortOrder   Int               @default(0)
  parentId    Int?
  level       Int               @default(1)
  path        String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  artworks    Artwork[]
  parent      ArtworkCategory?  @relation("ArtworkCategoryHierarchy", fields: [parentId], references: [id])
  children    ArtworkCategory[] @relation("ArtworkCategoryHierarchy")
}

model ArtworkTag {
  id          Int                     @id @default(autoincrement())
  name        String                  @unique
  color       String?
  description String?
  isActive    Boolean                 @default(true)
  createdAt   DateTime                @default(now())
  updatedAt   DateTime                @updatedAt
  artworks    ArtworkTagsOnArtworks[]
}

model ArtworkTagsOnArtworks {
  artworkId Int
  tagId     Int
  artwork   Artwork    @relation(fields: [artworkId], references: [id], onDelete: Cascade)
  tag       ArtworkTag @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([artworkId, tagId])
}

model PosSale {
  id            Int           @id @default(autoincrement())
  employeeId    Int
  customerId    Int?
  customerInfo  Json?
  totalAmount   Float
  paymentMethod String        @default("cash")
  date          DateTime      @default(now())
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  employee      Employee      @relation(fields: [employeeId], references: [id])
  items         PosSaleItem[]
}

model PosSaleItem {
  id        Int      @id @default(autoincrement())
  posSaleId Int
  productId Int?
  quantity  Int
  price     Float
  discount  Float    @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  posSale   PosSale  @relation(fields: [posSaleId], references: [id], onDelete: Cascade)
  product   Product? @relation(fields: [productId], references: [id])
}

model ChannelDeposit {
  id            Int      @id @default(autoincrement())
  channelId     Int
  amount        Float
  type          String
  date          DateTime
  paymentMethod String?
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  channel       Channel  @relation(fields: [channelId], references: [id])
}

model ChannelSale {
  id           Int                @id @default(autoincrement())
  channelId    Int
  saleDate     DateTime
  totalAmount  Float
  notes        String?
  status       String             @default("pending")
  importSource String?
  settlementId Int?
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt
  channel      Channel            @relation(fields: [channelId], references: [id])
  settlement   ChannelSettlement? @relation(fields: [settlementId], references: [id])
  items        ChannelSaleItem[]
}

model ChannelSaleItem {
  id                 Int              @id @default(autoincrement())
  channelSaleId      Int
  productId          Int?
  channelInventoryId Int
  quantity           Int
  price              Float
  notes              String?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  channelInventory   ChannelInventory @relation(fields: [channelInventoryId], references: [id])
  channelSale        ChannelSale      @relation(fields: [channelSaleId], references: [id], onDelete: Cascade)
  product            Product?         @relation(fields: [productId], references: [id])
}

model ChannelSettlement {
  id            Int              @id @default(autoincrement())
  channelId     Int
  settlementNo  String           @unique
  startDate     DateTime
  endDate       DateTime
  totalAmount   Float
  paidAmount    Float            @default(0)
  status        String           @default("draft")
  paymentDate   DateTime?
  paymentMethod String?
  notes         String?
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  invoices      ChannelInvoice[]
  sales         ChannelSale[]
  channel       Channel          @relation(fields: [channelId], references: [id])
}

model ChannelInvoice {
  id           Int               @id @default(autoincrement())
  settlementId Int
  invoiceNo    String?
  invoiceDate  DateTime?
  amount       Float
  imageUrl     String?
  status       String            @default("pending")
  notes        String?
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  settlement   ChannelSettlement @relation(fields: [settlementId], references: [id])
}

model ChannelDistribution {
  id                 Int              @id @default(autoincrement())
  channelId          Int
  channelInventoryId Int
  quantity           Int
  distributionDate   DateTime
  notes              String?
  status             String           @default("pending")
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  channel            Channel          @relation(fields: [channelId], references: [id])
  channelInventory   ChannelInventory @relation(fields: [channelInventoryId], references: [id])
}

model FinancialAccount {
  id             Int                    @id @default(autoincrement())
  name           String
  accountNumber  String?
  accountType    String
  bankName       String?
  initialBalance Float                  @default(0)
  currentBalance Float                  @default(0)
  isActive       Boolean                @default(true)
  notes          String?
  createdAt      DateTime               @default(now())
  updatedAt      DateTime               @updatedAt
  transactions   FinancialTransaction[] @relation("AccountTransactions")
}

model FinancialCategory {
  id           Int                    @id @default(autoincrement())
  name         String
  type         String
  code         String                 @unique
  parentId     Int?
  description  String?
  isSystem     Boolean                @default(false)
  isActive     Boolean                @default(true)
  createdAt    DateTime               @default(now())
  updatedAt    DateTime               @updatedAt
  parent       FinancialCategory?     @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children     FinancialCategory[]    @relation("CategoryHierarchy")
  transactions FinancialTransaction[] @relation("CategoryTransactions")
}

model FinancialTransaction {
  id              Int                @id @default(autoincrement())
  transactionDate DateTime
  amount          Float
  type            String
  accountId       Int
  categoryId      Int?
  paymentMethod   String?
  relatedId       Int?
  relatedType     String?
  counterparty    String?
  notes           String?
  attachmentUrl   String?
  status          String             @default("completed")
  createdById     String?
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt
  account         FinancialAccount   @relation("AccountTransactions", fields: [accountId], references: [id])
  category        FinancialCategory? @relation("CategoryTransactions", fields: [categoryId], references: [id])
}

model DataBackup {
  id           Int       @id @default(autoincrement())
  name         String
  description  String?
  type         String
  status       String    @default("pending")
  filePath     String?
  fileSize     Int?
  modules      String[]
  startTime    DateTime  @default(now())
  endTime      DateTime?
  duration     Int?
  errorMessage String?
  createdBy    String
  isEncrypted  Boolean   @default(true)
  checksum     String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  @@index([status])
  @@index([createdBy])
  @@index([startTime])
}

model DataTemplate {
  id          Int      @id @default(autoincrement())
  name        String
  type        String
  module      String
  description String?
  fields      Json
  mapping     Json?
  validation  Json?
  example     Json?
  version     String   @default("1.0")
  isActive    Boolean  @default(true)
  isSystem    Boolean  @default(false)
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([type])
  @@index([module])
  @@index([isActive])
}

model SystemAnnouncement {
  id          Int                @id @default(autoincrement())
  title       String
  content     String
  type        String             @default("info")
  priority    String             @default("normal")
  targetUsers String[]
  displayType String             @default("banner")
  startTime   DateTime           @default(now())
  endTime     DateTime?
  isActive    Boolean            @default(true)
  requireRead Boolean            @default(false)
  createdBy   String
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  readRecords AnnouncementRead[]

  @@index([isActive])
  @@index([startTime])
  @@index([type])
}

model AnnouncementRead {
  id             Int                @id @default(autoincrement())
  announcementId Int
  userId         String
  readAt         DateTime           @default(now())
  announcement   SystemAnnouncement @relation(fields: [announcementId], references: [id], onDelete: Cascade)

  @@unique([announcementId, userId])
  @@index([userId])
}

model SystemMetrics {
  id         Int      @id @default(autoincrement())
  metricType String
  metricName String
  value      Float
  unit       String?
  threshold  Float?
  status     String   @default("normal")
  details    Json?
  timestamp  DateTime @default(now())
  createdAt  DateTime @default(now())

  @@index([metricType])
  @@index([timestamp])
  @@index([status])
}

model PrintTemplate {
  id          Int      @id @default(autoincrement())
  name        String
  type        String
  description String?
  template    Json
  paperSize   String   @default("A4")
  orientation String   @default("portrait")
  isDefault   Boolean  @default(false)
  isActive    Boolean  @default(true)
  version     String   @default("1.0")
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([type])
  @@index([isActive])
  @@index([isDefault])
}

model FinanceRecord {
  id              Int       @id @default(autoincrement())
  type            String
  amount          Float
  description     String
  referenceId     String?
  referenceType   String?
  supplierId      Int?
  productId       Int?
  accountId       Int?
  operatorId      String?
  transactionDate DateTime  @default(now())
  dueDate         DateTime?
  notes           String?
  status          String    @default("active")
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  product         Product?  @relation(fields: [productId], references: [id])
  supplier        Supplier? @relation(fields: [supplierId], references: [id])

  @@index([type])
  @@index([referenceId])
  @@index([referenceType])
  @@index([supplierId])
  @@index([transactionDate])
  @@index([status])
}
